"""
Authentication Routes for v2 API

This module contains the FastAPI routes for authentication endpoints.
Implements the Register API with microservices architecture.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from db.session import get_db
from .models import UserRegistrationRequest, UserRegistrationResponse, UserLoginRequest, TokenResponse, RefreshTokenRequest, LogoutRequest, SuccessResponse, ErrorResponse
from .service import UserRegistrationService, UserAuthenticationService


# Create router for authentication endpoints
router = APIRouter(
    prefix="/auth",
    tags=["authentication-v2"],
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        409: {"model": ErrorResponse, "description": "Conflict"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)


@router.post(
    "/register",
    response_model=UserRegistrationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user",
    description="Register a new user in the system with the provided information. "
                "This endpoint creates a user account and assigns the specified roles.",
    responses={
        201: {
            "description": "User successfully registered",
            "model": UserRegistrationResponse
        },
        400: {
            "description": "Invalid input data",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "VALIDATION_ERROR",
                        "message": "Invalid input data",
                        "details": {
                            "field": "email",
                            "code": "INVALID_EMAIL_FORMAT"
                        }
                    }
                }
            }
        },
        409: {
            "description": "User already exists",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "CONFLICT",
                        "message": "User with this username or email already exists",
                        "details": {
                            "field": "username",
                            "code": "DUPLICATE_VALUE"
                        }
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "INTERNAL_ERROR",
                        "message": "An internal error occurred while processing the request",
                        "details": {}
                    }
                }
            }
        }
    }
)
async def register_user(
    user_data: UserRegistrationRequest,
    db: Session = Depends(get_db)
) -> UserRegistrationResponse:
    """
    Register a new user.
    
    This endpoint creates a new user account with the provided information.
    The user will be assigned the specified roles and associated with a tenant.
    
    Args:
        user_data: User registration data including username, email, password, etc.
        db: Database session dependency
        
    Returns:
        UserRegistrationResponse: Created user information
        
    Raises:
        HTTPException: 
            - 400 if input validation fails
            - 409 if user already exists
            - 500 if internal error occurs
    """
    try:
        # Initialize the registration service
        registration_service = UserRegistrationService(db)
        
        # Attempt to register the user
        created_user = registration_service.register_user(user_data)
        
        if not created_user:
            # Check if it's a duplicate user error
            if registration_service._check_user_exists(user_data.username, user_data.email):
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail={
                        "error": "CONFLICT",
                        "message": "User with this username or email already exists",
                        "details": {
                            "username": user_data.username,
                            "email": user_data.email
                        }
                    }
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={
                        "error": "INTERNAL_ERROR",
                        "message": "Failed to create user account",
                        "details": {}
                    }
                )
        
        return created_user
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error and return a generic error response
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error during user registration: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while processing the request",
                "details": {}
            }
        )


@router.get(
    "/user/{user_id}",
    response_model=UserRegistrationResponse,
    summary="Get user by ID",
    description="Retrieve user information by user ID",
    responses={
        200: {
            "description": "User found",
            "model": UserRegistrationResponse
        },
        404: {
            "description": "User not found",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "NOT_FOUND",
                        "message": "User not found",
                        "details": {
                            "user_id": "123e4567-e89b-12d3-a456-426614174000"
                        }
                    }
                }
            }
        }
    }
)
async def get_user(
    user_id: str,
    db: Session = Depends(get_db)
) -> UserRegistrationResponse:
    """
    Get user information by ID.
    
    Args:
        user_id: User ID to retrieve
        db: Database session dependency
        
    Returns:
        UserRegistrationResponse: User information
        
    Raises:
        HTTPException: 404 if user not found
    """
    try:
        registration_service = UserRegistrationService(db)
        user = registration_service.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "User not found",
                    "details": {
                        "user_id": user_id
                    }
                }
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving user {user_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving user information",
                "details": {}
            }
        )


@router.post(
    "/login",
    response_model=TokenResponse,
    summary="User login",
    description="Authenticate user with username/email and password, returns JWT tokens",
    responses={
        200: {
            "description": "Login successful",
            "model": TokenResponse
        },
        401: {
            "description": "Invalid credentials",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "AUTHENTICATION_FAILED",
                        "message": "Invalid username or password",
                        "details": {}
                    }
                }
            }
        },
        422: {
            "description": "Validation error",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "VALIDATION_ERROR",
                        "message": "Invalid input data",
                        "details": {
                            "field": "username",
                            "code": "FIELD_REQUIRED"
                        }
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "INTERNAL_ERROR",
                        "message": "An internal error occurred during authentication",
                        "details": {}
                    }
                }
            }
        }
    }
)
async def login_user(
    login_data: UserLoginRequest,
    db: Session = Depends(get_db)
) -> TokenResponse:
    """
    Authenticate user and return JWT tokens.
    
    This endpoint authenticates a user with their username/email and password.
    Upon successful authentication, it returns JWT access and refresh tokens
    along with user information.
    
    Args:
        login_data: Login credentials (username/email and password)
        db: Database session dependency
        
    Returns:
        TokenResponse: JWT tokens and user information
        
    Raises:
        HTTPException: 
            - 401 if authentication fails
            - 422 if input validation fails
            - 500 if internal error occurs
    """
    try:
        # Initialize the authentication service
        auth_service = UserAuthenticationService(db)
        
        # Attempt to authenticate the user
        token_response = auth_service.authenticate_user(login_data)
        
        if not token_response:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "AUTHENTICATION_FAILED",
                    "message": "Invalid username or password",
                    "details": {}
                }
            )
        
        return token_response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error and return a generic error response
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error during authentication: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred during authentication",
                "details": {}
            }
        )


@router.post(
    "/refresh",
    response_model=TokenResponse,
    summary="Refresh access token",
    description="Refresh access token using refresh token",
    responses={
        200: {
            "description": "Token refreshed successfully",
            "model": TokenResponse
        },
        401: {
            "description": "Invalid or expired refresh token",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "TOKEN_INVALID",
                        "message": "Invalid or expired refresh token",
                        "details": {}
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "model": ErrorResponse
        }
    }
)
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    db: Session = Depends(get_db)
) -> TokenResponse:
    """
    Refresh access token using refresh token.
    
    Args:
        refresh_request: Refresh token request
        db: Database session dependency
        
    Returns:
        TokenResponse: New JWT tokens and user information
        
    Raises:
        HTTPException: 
            - 401 if refresh token is invalid or expired
            - 500 if internal error occurs
    """
    try:
        auth_service = UserAuthenticationService(db)
        token_response = auth_service.refresh_access_token(refresh_request)
        
        if not token_response:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "TOKEN_INVALID",
                    "message": "Invalid or expired refresh token",
                    "details": {}
                }
            )
        
        return token_response
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error during token refresh: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred during token refresh",
                "details": {}
            }
        )


@router.post(
    "/logout",
    response_model=SuccessResponse,
    summary="User logout",
    description="Logout user by revoking tokens",
    responses={
        200: {
            "description": "Logout successful",
            "model": SuccessResponse
        },
        500: {
            "description": "Internal server error",
            "model": ErrorResponse
        }
    }
)
async def logout_user(
    logout_request: LogoutRequest,
    db: Session = Depends(get_db)
) -> SuccessResponse:
    """
    Logout user by revoking tokens.
    
    Args:
        logout_request: Logout request with optional refresh token
        db: Database session dependency
        
    Returns:
        SuccessResponse: Success response
    """
    try:
        auth_service = UserAuthenticationService(db)
        return auth_service.logout_user(logout_request)
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error during logout: {str(e)}")
        
        # Return success anyway to avoid exposing errors
        return SuccessResponse(
            success=True,
            message="Successfully logged out",
            data={}
        )
