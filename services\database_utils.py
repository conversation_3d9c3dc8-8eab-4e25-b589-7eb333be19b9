from sqlalchemy import MetaData, Table, Column, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, text
from sqlalchemy.orm import Session
from database.postgres import get_db
import re

class CaseConverter:
    """
    Utility class to handle case conversion between different naming conventions.
    """
    @staticmethod
    def to_snake_case(name):
        """Convert camelCase or PascalCase to snake_case"""
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', name)
        return name.lower()
    
    @staticmethod
    def to_camel_case(name):
        """Convert snake_case to camelCase"""
        components = name.split('_')
        return components[0] + ''.join(x.title() for x in components[1:])
    
    @staticmethod
    def to_pascal_case(name):
        """Convert snake_case to PascalCase"""
        return ''.join(x.title() for x in name.split('_'))

def create_runtime_tables(db: Session):
    """
    Dynamically creates runtime tables based on entity definitions stored in the configuration database.
    """
    try:
        metadata = MetaData()
        metadata.reflect(bind=db.get_bind())  # Ensure metadata is loaded

        # Fetch entity definitions from PostgreSQL
        query = text("SELECT entity_id, name FROM workflow_runtime.entities")
        entities = db.execute(query).fetchall()

        for entity in entities:
            # Fix: Access tuple elements by index or convert to dict
            # Option 1: Use indices
            entity_id = entity[0]  # Access by index
            entity_name = entity[1]  # Access by index
            
            # Standardize entity name to snake_case
            standardized_entity_name = CaseConverter.to_snake_case(entity_name)
            
            # Fetch attributes for this entity
            query_attributes = text("SELECT attribute_id, name, datatype FROM workflow_runtime.entity_attributes WHERE entity_id = :entity_id")
            attributes = db.execute(query_attributes, {"entity_id": entity_id}).fetchall()

            # ✅ Step 4: Define table dynamically (Check if already exists)
            if standardized_entity_name in metadata.tables:
                print(f"⚠️ Table '{standardized_entity_name}' already exists. Skipping creation.")
                continue  # ✅ Skip table creation if it already exists

            # Define table dynamically
            columns = [
                Column("id", Integer, primary_key=True)  # Every table must have an ID
            ]

            for attr in attributes:
                # Fix: Access tuple elements by index
                attr_name = attr[1]  # Assuming 'name' is the second column
                attr_type = attr[2].upper()  # Assuming 'data_type' is the third column
                
                # Standardize attribute name to snake_case
                standardized_attr_name = CaseConverter.to_snake_case(attr_name)

                # Map data types
                if attr_type in ["TEXT", "STRING"]:
                    col_type = String(255)
                elif attr_type in ["INTEGER", "INT"]:
                    col_type = Integer
                else:
                    col_type = String(255)  # Default fallback

                columns.append(Column(standardized_attr_name, col_type))

            # Create table dynamically
            table = Table(standardized_entity_name, metadata, *columns, extend_existing=True)
            table.create(db.get_bind(), checkfirst=True)

        print("✅ Runtime tables created successfully.")
        return True
    except Exception as e:
        print(f"❌ Error creating runtime tables: {e}")
        raise

def get_standardized_table_name(entity_name):
    """
    Returns the standardized snake_case version of an entity name.
    Use this when referencing tables in SQL queries.
    """
    return CaseConverter.to_snake_case(entity_name)

def get_standardized_column_name(attribute_name):
    """
    Returns the standardized snake_case version of an attribute name.
    Use this when referencing columns in SQL queries.
    """
    return CaseConverter.to_snake_case(attribute_name)
