#!/usr/bin/env python3
"""
Database check script to find existing workflow instances and test data.

This script connects directly to the database to check what test data is available
for testing the local objectives functionality.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:workflow_postgres_secure_password@localhost:5433/workflow_system")

def get_db_connection():
    """Get database connection."""
    try:
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        return engine, SessionLocal()
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return None, None

def check_workflow_instances(db):
    """Check existing workflow instances."""
    print("🔍 Checking workflow instances...")
    
    try:
        query = """
        SELECT instance_id, go_id, tenant_id, status, started_by, started_at, current_lo_id
        FROM workflow_runtime.workflow_instances 
        ORDER BY started_at DESC 
        LIMIT 10
        """
        result = db.execute(text(query)).fetchall()
        
        if result:
            print(f"✅ Found {len(result)} workflow instances:")
            for row in result:
                print(f"   - {row.instance_id} | GO: {row.go_id} | Status: {row.status} | LO: {row.current_lo_id}")
            return [row.instance_id for row in result]
        else:
            print("❌ No workflow instances found")
            return []
            
    except Exception as e:
        print(f"❌ Error checking workflow instances: {e}")
        return []

def check_global_objectives(db):
    """Check existing global objectives."""
    print("\n🎯 Checking global objectives...")
    
    try:
        query = """
        SELECT go_id, name, description, tenant_id
        FROM workflow_runtime.global_objectives 
        ORDER BY created_at DESC 
        LIMIT 5
        """
        result = db.execute(text(query)).fetchall()
        
        if result:
            print(f"✅ Found {len(result)} global objectives:")
            for row in result:
                print(f"   - {row.go_id}: {row.name}")
            return [row.go_id for row in result]
        else:
            print("❌ No global objectives found")
            return []
            
    except Exception as e:
        print(f"❌ Error checking global objectives: {e}")
        return []

def check_local_objectives(db):
    """Check existing local objectives."""
    print("\n🎯 Checking local objectives...")
    
    try:
        query = """
        SELECT lo_id, name, go_id, sequence_number
        FROM workflow_runtime.local_objectives 
        ORDER BY go_id, sequence_number 
        LIMIT 10
        """
        result = db.execute(text(query)).fetchall()
        
        if result:
            print(f"✅ Found {len(result)} local objectives:")
            for row in result:
                print(f"   - {row.lo_id}: {row.name} (GO: {row.go_id}, Seq: {row.sequence_number})")
            return result
        else:
            print("❌ No local objectives found")
            return []
            
    except Exception as e:
        print(f"❌ Error checking local objectives: {e}")
        return []

def check_input_execution_data(db):
    """Check existing input execution data."""
    print("\n📊 Checking input execution data...")
    
    try:
        query = """
        SELECT instance_id, lo_id, input_contextual_id, input_value, created_at
        FROM workflow_runtime.lo_input_execution 
        ORDER BY created_at DESC 
        LIMIT 10
        """
        result = db.execute(text(query)).fetchall()
        
        if result:
            print(f"✅ Found {len(result)} input execution records:")
            for row in result:
                value_preview = str(row.input_value)[:50] if row.input_value else "NULL"
                print(f"   - Instance: {row.instance_id} | LO: {row.lo_id} | Value: {value_preview}")
        else:
            print("❌ No input execution data found")
            
    except Exception as e:
        print(f"❌ Error checking input execution data: {e}")

def create_test_instance(db):
    """Create a test workflow instance for testing."""
    print("\n🛠️ Creating test workflow instance...")
    
    try:
        # First check if we have any global objectives
        go_query = "SELECT go_id FROM workflow_runtime.global_objectives LIMIT 1"
        go_result = db.execute(text(go_query)).fetchone()
        
        if not go_result:
            print("❌ No global objectives found - cannot create test instance")
            return None
        
        go_id = go_result.go_id
        instance_id = "test-instance-001"
        
        # Create test instance
        create_query = """
        INSERT INTO workflow_runtime.workflow_instances (
            instance_id, go_id, tenant_id, status, started_by, started_at,
            current_lo_id, instance_data, is_test, version
        ) VALUES (
            :instance_id, :go_id, 'test_tenant', 'In Progress', 'test_user_id', NOW(),
            NULL, '{}', TRUE, '1.0'
        )
        ON CONFLICT (instance_id) DO UPDATE SET
            status = 'In Progress',
            started_at = NOW()
        RETURNING instance_id
        """
        
        result = db.execute(text(create_query), {
            "instance_id": instance_id,
            "go_id": go_id
        })
        db.commit()
        
        print(f"✅ Created/updated test instance: {instance_id}")
        return instance_id
        
    except Exception as e:
        print(f"❌ Error creating test instance: {e}")
        db.rollback()
        return None

def main():
    """Main function."""
    print("🔍 Database Check - Workflow System")
    print("=" * 50)
    
    engine, db = get_db_connection()
    if not db:
        sys.exit(1)
    
    try:
        # Check existing data
        instances = check_workflow_instances(db)
        gos = check_global_objectives(db)
        los = check_local_objectives(db)
        check_input_execution_data(db)
        
        # Create test instance if needed
        if not instances and gos:
            test_instance = create_test_instance(db)
            if test_instance:
                print(f"\n🎯 Use this instance ID for testing: {test_instance}")
        elif instances:
            print(f"\n🎯 Use any of these instance IDs for testing: {instances[0]}")
        
        print("\n" + "=" * 50)
        print("✅ Database check completed")
        
    finally:
        db.close()

if __name__ == "__main__":
    main()
