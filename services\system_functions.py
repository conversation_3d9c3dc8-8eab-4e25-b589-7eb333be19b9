"""
System functions module that defines all the functions to be registered 
with the function repository
"""
from typing import Dict, Any, List, Optional, Union, Tuple
import re
import json
import datetime
import string
import random
import hashlib
import uuid
from sqlalchemy.orm import Session
from sqlalchemy import text
from services.function_repository import function_repository
from services.database_utils import CaseConverter
from decimal import Decimal, ROUND_HALF_UP

print(f"DEBUG: Registering functions...")

# Define the initialization function
def init_system_functions():
    """
    Initialize system functions and verify they are registered.
    This just checks for registered functions and returns statistics.
    """
    categories = function_repository.list_categories()
    functions = function_repository.list_functions()
    
    print(f"✅ System functions initialized with {len(categories)} categories")
    print(f"✅ Categories: {', '.join(categories)}")
    
    func_count = sum(len(funcs) for funcs in functions.values())
    print(f"✅ Total of {func_count} functions registered")
    
    # Verify each category has at least one function
    for category, funcs in functions.items():
        print(f"  - Category '{category}' has {len(funcs)} functions: {', '.join(funcs)}")
    
    return func_count > 0  # Return True if at least one function registered

# Database Functions
@function_repository.register("database", "create")
def create(db: Session, entity_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Insert a single record into a PostgreSQL table using SQLAlchemy.

    Args:
        db (Session): PostgreSQL database session.
        entity_id (str): Entity ID (e.g., "E002")
        data (dict): Dictionary of attribute values.

    Returns:
        dict: Inserted record.
    """
    from services.database_utils import CaseConverter  # Import the standardization utilities
    
    # ✅ Fetch actual entity table name
    table_mapping_query = "SELECT name FROM workflow_runtime.entities WHERE entity_id = :entity_id"
    table_result = db.execute(text(table_mapping_query), {"entity_id": entity_id}).fetchone()
    if not table_result:
        raise ValueError(f"Entity '{entity_id}' not found in workflow_runtime.entities")

    # ✅ Get the entity name and standardize it to snake_case with z_entity_ prefix
    entity_name = table_result[0]
    standardized_table = f"z_entity_{CaseConverter.to_snake_case(entity_name)}"
    
    print(f"DEBUG: Entity name: {entity_name} -> Standardized table: {standardized_table}")

    # ✅ Fetch actual attribute names
    attributes_query = "SELECT attribute_id, name FROM workflow_runtime.entity_attributes WHERE entity_id = :entity_id"
    attributes_result = db.execute(text(attributes_query), {"entity_id": entity_id}).fetchall()
    
    if not attributes_result:
        raise ValueError(f"No attributes found for entity '{entity_id}'")
        
    # ✅ Create mapping for attribute IDs to standardized column names
    attribute_mapping = {}
    for row in attributes_result:
        attr_id = row[0]
        attr_name = row[1]
        standardized_attr_name = CaseConverter.to_snake_case(attr_name)
        attribute_mapping[attr_id] = standardized_attr_name
        print(f"DEBUG: Attribute mapping: {attr_id} -> {attr_name} -> {standardized_attr_name}")

    # ✅ Check if we have data for the attributes
    if not data:
        print(f"DEBUG: No data provided. Using placeholder values.")
        # Create placeholder data for demonstration
        data = {attr_id: None for attr_id in attribute_mapping.keys()}

    # ✅ Create standardized column names and placeholders
    try:
        # Only include attributes that are in the mapping
        valid_attrs = [attr for attr in data.keys() if attr in attribute_mapping]
        
        if not valid_attrs:
            raise ValueError(f"None of the provided attributes match the entity definition. Check your input data.")
            
        columns = ", ".join([attribute_mapping[attr] for attr in valid_attrs])
        placeholders = {attribute_mapping[attr]: data[attr] for attr in valid_attrs}
        
        values_str = ", ".join([f":{attribute_mapping[attr]}" for attr in valid_attrs
        # ✅ Add audit fields
#        now = datetime.datetime.now()
 #       placeholders["created_at"] = now
  #      placeholders["updated_at"] = now
   #     columns += ", created_at, updated_at"
    #    values_str += ", :created_at, :updated_at"

            ])
     

# ✅ Add audit fields
        now = datetime.datetime.now()
       # placeholders["created_at"] = now
       # placeholders["updated_at"] = now
       # columns += ", created_at, updated_at"
       # values_str += ", :created_at, :updated_at"
        print(f"DEBUG: Columns: {columns}")
        print(f"DEBUG: Values string: {values_str}")
        print(f"DEBUG: Placeholders: {placeholders}")
        
        # ✅ Ensure schema is included
        full_table_name = f"workflow_runtime.{standardized_table}"
        
        # ✅ Construct and execute query with proper casing and schema
        query = text(f"INSERT INTO {full_table_name} ({columns}) VALUES ({values_str}) RETURNING *")
        result = db.execute(query, placeholders)
        db.commit()

        print(f"DEBUG: Registered 'create' function in 'database' category")
        print(f"DEBUG: Current functions in repository: {function_repository.list_functions()}")
        
        return result.fetchone()._asdict()
    except Exception as e:
        db.rollback()
        print(f"DEBUG: Error in create function: {str(e)}")
        raise ValueError(f"Error executing create function: {str(e)}")


@function_repository.register("database", "fetch")
def fetch(db: Session, table: str, filters: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Fetch a single record from a PostgreSQL table."""
    where_clause = " AND ".join([f"{key} = :{key}" for key in filters.keys()])
    query = text(f"SELECT * FROM {table} WHERE {where_clause} LIMIT 1")

    result = db.execute(query, filters).fetchone()
    return result._asdict() if result else None

@function_repository.register("database", "fetch_records")
def fetch_records(
    db: Session,
    table: str,
    filters: Optional[Dict[str, Any]] = None,
    limit: int = 10,
    offset: int = 0,
    order_by: str = "id"
) -> List[Dict[str, Any]]:
    print("✅ fetch_records called with db =", type(db), "table =", table)

    where_clause = " AND ".join([f"{key} = :{key}" for key in filters.keys()]) if filters else "1=1"
    query = text(f"SELECT * FROM {table} WHERE {where_clause} ORDER BY {order_by} LIMIT {limit} OFFSET {offset}")

    result = db.execute(query, filters if filters else {}).fetchall()
    return [row._asdict() for row in result]



@function_repository.register("database", "fetch_enum_values")
def fetch_enum_values(db: Session, entity_id: str = None, attribute_id: str = None) -> List[Dict[str, Any]]:
    """
    Fetch enum values for an entity attribute.
    
    Args:
        db (Session): Database session
        entity_id (str): Entity ID (e.g., "E002")
        attribute_id (str): Attribute ID (e.g., "AT110")
        
    Returns:
        List[Dict[str, Any]]: List of enum values with value and display fields
    """
    try:
        if not attribute_id:
            return []
            
        # Query to get enum values
        query = text("""
            SELECT value, value as display 
            FROM workflow_runtime.attribute_enum_values 
            WHERE attribute_id = :attribute_id
            ORDER BY value
        """)
        
        results = db.execute(query, {"attribute_id": attribute_id.lower()}).fetchall()
        
        # Format the results as a list of dicts with value and display properties
        return [{"value": row[0], "display": row[1]} for row in results]
    except Exception as e:
        print(f"❌ Error fetching enum values: {e}")
        return []

@function_repository.register("database", "fetch_filtered_records")
def fetch_filtered_records(
    db: Session,
    table: str,
    filter_column: str,
    filter_value: Any,
    value_column: str,
    display_column: str = None,
    additional_filters: Dict[str, Any] = None
) -> List[Dict[str, Any]]:
    """
    Generic function to fetch filtered records for dependent dropdowns.
    
    Args:
        db (Session): Database session
        table (str): Table name
        filter_column (str): Column to filter on
        filter_value (Any): Value to filter by
        value_column (str): Column to use for option value
        display_column (str, optional): Column to use for option display
        additional_filters (Dict[str, Any], optional): Additional filter conditions
        
    Returns:
        List[Dict[str, Any]]: List of options with value and display fields
    """
    import traceback  # Add missing import
    
    try:
        # Start building the query
        query_str = f"SELECT {value_column}"
        
        # Add display column if provided, otherwise use value column
        if display_column:
            query_str += f", {display_column}"
        else:
            query_str += f", {value_column} as display"
            
        # Base query
        query_str += f" FROM {table} WHERE"
        
        # First, check if the column is a boolean type by querying the database schema
        is_boolean_column = False
        try:
            # Extract schema and table name
            parts = table.split('.')
            schema_name = parts[0] if len(parts) > 1 else 'public'
            table_name = parts[-1]
            
            # Query to check column type
            type_check_query = text("""
                SELECT data_type 
                FROM information_schema.columns 
                WHERE table_schema = :schema 
                AND table_name = :table 
                AND column_name = :column
            """)
            
            column_type = db.execute(
                type_check_query, 
                {"schema": schema_name, "table": table_name, "column": filter_column}
            ).scalar()
            
            is_boolean_column = column_type and column_type.lower() == 'boolean'
            print(f"DEBUG: Column '{filter_column}' type: {column_type}, is_boolean: {is_boolean_column}")
        except Exception as e:
            print(f"DEBUG: Error checking column type: {e}")
            # If we can't determine the type, proceed with type-based checks on the value
        
        # Handle filter based on column type and value type
        if is_boolean_column:
            # For boolean columns, convert string values like 'true'/'false' to actual booleans
            actual_value = filter_value
            if isinstance(filter_value, str):
                actual_value = filter_value.lower() == 'true'
                params = {"filter_value": actual_value}
                print(f"DEBUG: Converting string '{filter_value}' to boolean {actual_value}")
            else:
                params = {"filter_value": filter_value}
            
            # Direct comparison for boolean columns (no LOWER())
            query_str += f" {filter_column} = :filter_value"
        elif isinstance(filter_value, bool):
            # Direct comparison for boolean values
            query_str += f" {filter_column} = :filter_value"
            params = {"filter_value": filter_value}
        elif isinstance(filter_value, (int, float)):
            # Direct comparison for numeric values
            query_str += f" {filter_column} = :filter_value"
            params = {"filter_value": filter_value}
        else:
            # Case-insensitive comparison for string values
            query_str += f" LOWER({filter_column}) = LOWER(:filter_value)"
            params = {"filter_value": filter_value}
        
        # Add any additional filters
        if additional_filters:
            for idx, (key, val) in enumerate(additional_filters.items()):
                # Check if this column is boolean
                is_bool_column = False
                try:
                    col_type = db.execute(
                        type_check_query, 
                        {"schema": schema_name, "table": table_name, "column": key}
                    ).scalar()
                    is_bool_column = col_type and col_type.lower() == 'boolean'
                except Exception:
                    pass
                
                # Handle different data types appropriately
                if is_bool_column:
                    # For boolean columns, convert string values
                    if isinstance(val, str):
                        actual_val = val.lower() == 'true'
                        params[f"add_filter_{idx}"] = actual_val
                    else:
                        params[f"add_filter_{idx}"] = val
                    query_str += f" AND {key} = :add_filter_{idx}"
                elif isinstance(val, bool):
                    # Direct comparison for booleans (no LOWER())
                    query_str += f" AND {key} = :add_filter_{idx}"
                    params[f"add_filter_{idx}"] = val
                elif isinstance(val, (int, float)):
                    # Direct comparison for numbers (no LOWER())
                    query_str += f" AND {key} = :add_filter_{idx}"
                    params[f"add_filter_{idx}"] = val
                else:
                    # Case-insensitive for strings
                    query_str += f" AND LOWER({key}) = LOWER(:add_filter_{idx})"
                    params[f"add_filter_{idx}"] = val
                
        query_str += f" ORDER BY {value_column}"
        
        print(f"DEBUG: fetch_filtered_records query: {query_str}")
        print(f"DEBUG: Parameters: {params}")
        
        # Execute query
        results = db.execute(text(query_str), params).fetchall()
        
        print(f"DEBUG: fetch_filtered_records found {len(results)} results")
        
        # Format the results
        formatted_results = []
        if display_column:
            formatted_results = [{"value": row[0], "display": row[1]} for row in results]
        else:
            formatted_results = [{"value": row[0], "display": row[0]} for row in results]
            
        # Log the formatted results for debugging
        print(f"DEBUG: Formatted results: {formatted_results}")
        
        return formatted_results
    except Exception as e:
        print(f"❌ Error fetching filtered records: {e}")
        traceback.print_exc()  # Print the full stack trace for debugging
        return []



###################################################
###New Function for Batch Update (To be tested)
###Date 7/5/25
###################################################

@function_repository.register("database", "batch_update")
def batch_update(db: Session, entity_id: str, records: List[Dict[str, Any]], lo_id: str = None) -> Dict[str, Any]:
    """
    Update multiple records in a single operation.
    
    Args:
        db (Session): PostgreSQL database session
        entity_id (str): Entity ID (e.g., "e001")
        records (List[Dict[str, Any]]): List of records to update, each containing:
            - lookup fields for identifying the record
            - update fields with new values
        lo_id (str, optional): Local Objective ID for metadata retrieval
        
    Returns:
        Dict[str, Any]: Status information about the batch update operation
    """
    from services.database_utils import CaseConverter
    
    # Get table name from entity_id
    table_name = db.execute(text("""
        SELECT name FROM workflow_runtime.entities WHERE entity_id = :entity_id
    """), {"entity_id": entity_id}).scalar()
    
    if not table_name:
        raise ValueError(f"[batch_update] Entity '{entity_id}' not found.")
    
    table = f"z_entity_{CaseConverter.to_snake_case(table_name)}"
    
    # Get attribute mappings
    attr_rows = db.execute(text("""
        SELECT attribute_id, name, datatype 
        FROM workflow_runtime.entity_attributes
        WHERE entity_id = :entity_id
    """), {"entity_id": entity_id}).fetchall()
    
    attr_map = {}
    type_map = {}
    for row in attr_rows:
        attr_id = row.attribute_id.lower()
        column_name = CaseConverter.to_snake_case(row.name)
        attr_map[attr_id] = column_name
        type_map[attr_id] = row.datatype.lower() if row.datatype else 'string'
    
    # Process each record
    results = []
    for record in records:
        try:
            # Use the existing update function for each record
            result = function_repository.auto_execute("update", db, entity_id, record, lo_id)
            results.append(result)
        except Exception as e:
            # Continue with other records even if one fails
            print(f"[batch_update] Error updating record: {str(e)}")
            results.append({"status": "error", "message": str(e)})
    
    # Return summary
    success_count = sum(1 for r in results if r.get("status") == "updated")
    failed_count = len(results) - success_count
    
    return {
        "status": "completed",
        "total": len(records),
        "success_count": success_count,
        "failed_count": failed_count,
        "results": results
    }

#################################################################


###################################################
###New Function for soft delete (To be tested)
###Date 7/5/25
###################################################

@function_repository.register("database", "soft_delete")
def soft_delete(db: Session, entity_id: str, data: Dict[str, Any], delete_field: str = "deleted_mark") -> Dict[str, Any]:
    """
    Mark a record as deleted without removing it from the database.
    
    Args:
        db (Session): PostgreSQL database session
        entity_id (str): Entity ID (e.g., "e001")
        data (Dict[str, Any]): Dictionary containing lookup fields to identify the record
        delete_field (str, optional): Field name to use for soft deletion mark. Defaults to "deleted_mark"
        
    Returns:
        Dict[str, Any]: Status information about the deletion operation
    """
    from services.database_utils import CaseConverter
    
    # Get table name from entity_id
    table_name = db.execute(text("""
        SELECT name FROM workflow_runtime.entities WHERE entity_id = :entity_id
    """), {"entity_id": entity_id}).scalar()
    
    if not table_name:
        raise ValueError(f"[soft_delete] Entity '{entity_id}' not found.")
    
    table = f"z_entity_{CaseConverter.to_snake_case(table_name)}"
    
    # Create update data with deletion mark
    update_data = data.copy()
    update_data[delete_field] = True
    
    # If there's an updated_at field, update it too
    current_time = datetime.datetime.now()
    if "updated_at" in update_data or any(k.lower() == "updated_at" for k in update_data.keys()):
        update_data["updated_at"] = current_time
    
    # Use the existing update function
    result = function_repository.auto_execute("update", db, entity_id, update_data)
    
    if result.get("status") == "updated":
        return {
            "status": "deleted",
            "id": result.get("id"),
            "message": f"Record marked as deleted using {delete_field} field"
        }
    else:
        return {
            "status": "error",
            "message": "Failed to mark record as deleted"
        }
    

##################################################################################



@function_repository.register("database", "fetch_max_value")
def fetch_max_value(db: Session, entity: str, attribute: str) -> Union[int, float]:
    """Fetch the max value of a numeric column from an entity table."""
    table_name = db.execute(text("SELECT name FROM workflow_runtime.entities WHERE entity_id = :entity"), {"entity": entity}).scalar()
    column_name = db.execute(text("SELECT name FROM workflow_runtime.entity_attributes WHERE entity_id = :entity AND attribute_id = :attribute"), {"entity": entity, "attribute": attribute}).scalar()

    if not table_name or not column_name:
        raise ValueError("Entity or attribute not found.")

    query = text(f"SELECT MAX(CAST({column_name} AS NUMERIC)) FROM workflow_runtime.z_entity_{table_name.lower()}")
    return db.execute(query).scalar() or 0


@function_repository.register("database", "update")
def update(db: Session, entity_id: str, data: Dict[str, Any], lo_id: str = None) -> Dict[str, Any]:
    """
    Update records in a PostgreSQL table based on input data and metadata.
    
    Args:
        db (Session): PostgreSQL database session
        entity_id (str): Entity ID (e.g., "e001")
        data (Dict[str, Any]): Dictionary of attribute values to update
        lo_id (str, optional): Local Objective ID for metadata retrieval
        
    Returns:
        Dict[str, Any]: Status information about the update operation
    """
    from services.database_utils import CaseConverter

    # Step 1: Resolve table name
    table_name = db.execute(text("""
        SELECT name FROM workflow_runtime.entities WHERE entity_id = :entity_id
    """), {"entity_id": entity_id}).scalar()
    if not table_name:
        raise ValueError(f"[update] Entity '{entity_id}' not found.")
    table = f"z_entity_{CaseConverter.to_snake_case(table_name)}"

    # Step 2: Resolve attribute-to-column mapping
    attr_rows = db.execute(text("""
        SELECT attribute_id, name, datatype 
        FROM workflow_runtime.entity_attributes
        WHERE entity_id = :entity_id
    """), {"entity_id": entity_id}).fetchall()
    
    attr_map = {}
    type_map = {}
    for row in attr_rows:
        attr_id = row.attribute_id.lower()
        column_name = CaseConverter.to_snake_case(row.name)
        attr_map[attr_id] = column_name
        type_map[attr_id] = row.datatype.lower() if row.datatype else 'string'

    # Step 3: Fetch metadata usage (lookup/update) if lo_id is given
    metadata_map = {}
    if lo_id:
        print(f"[update] 📌 Fetching metadata for LO: {lo_id}")
        meta_rows = db.execute(text("""
            SELECT slot_id, metadata, id, contextual_id FROM workflow_runtime.lo_input_items
            WHERE lo_id = :lo_id
        """), {"lo_id": lo_id}).fetchall()

        for row in meta_rows:
            attr_id = row.slot_id.split(".")[1].lower()  # e.g., "at101"
            input_id = row.id  # e.g., "in008"
            
            # Handle different metadata formats
            usage = None
            try:
                # If metadata is already a dictionary (JSONB from PostgreSQL)
                if isinstance(row.metadata, dict):
                    usage = row.metadata.get("usage", "").lower()
                # If metadata is a JSON string
                elif isinstance(row.metadata, str):
                    if row.metadata.strip():
                        usage = json.loads(row.metadata).get("usage", "").lower()
                # If metadata is None or empty
                else:
                    usage = ""
            except Exception as e:
                print(f"[update] ⚠️ Failed to parse metadata for {row.slot_id}: {e}")
                usage = "update"  # Default to update if parsing fails
            
            # Store the usage type for this attribute
            metadata_map[attr_id] = usage
            
            # Also map by input_id for direct reference
            if input_id:
                metadata_map[input_id.lower()] = usage
                
            print(f"[update] 🔍 Mapped {attr_id} (input: {input_id}) → usage: '{usage}'")

    # Step 4: Build lookup and update field maps
    lookup_fields = {}
    update_fields = {}

    # Print debug information about the data and metadata
    print(f"[update] 📊 Input data keys: {list(data.keys())}")
    print(f"[update] 📊 Metadata map keys: {list(metadata_map.keys())}")

    for attr_id, value in data.items():
        if value is None or attr_id.lower() not in attr_map:
            continue

        attr_id_lower = attr_id.lower()
        column_name = attr_map[attr_id_lower]
        
        # Check metadata map for usage
        usage = metadata_map.get(attr_id_lower, "update")  # Default to update
        
        # Special case for "in" prefixed keys (direct input references)
        if attr_id_lower.startswith("in") and attr_id_lower in metadata_map:
            usage = metadata_map[attr_id_lower]
            print(f"[update] ✅ Found direct input metadata for {attr_id_lower}: {usage}")
        
        # CRITICAL FIX: Ensure proper type handling for lookup/update values
        # Get the data type for this attribute
        data_type = type_map.get(attr_id_lower, 'string')
        
        # Format value based on data type
        formatted_value = value
        if data_type == 'integer' and isinstance(value, int):
            # Keep it as an integer for numeric operations
            formatted_value = value
        elif data_type == 'integer' and not isinstance(value, int):
            # Try to convert string to integer
            try:
                formatted_value = int(value)
            except (ValueError, TypeError):
                # Fall back to string if conversion fails
                formatted_value = str(value)
        elif data_type == 'numeric' or data_type == 'float':
            # Try to convert to float
            try:
                formatted_value = float(value)
            except (ValueError, TypeError):
                formatted_value = str(value)
        elif data_type == 'date' and not isinstance(value, str):
            # Ensure dates are strings for database
            formatted_value = str(value)
        else:
            # Default to string for other types
            formatted_value = str(value) if value is not None else None
        
        # Distribute to the appropriate category based on usage
        if usage == "lookup" or usage == "both":
            lookup_fields[column_name] = formatted_value
            print(f"[update] 🔍 Using '{attr_id}' as lookup field: {formatted_value}")
        if usage == "update" or usage == "both" or not usage:
            update_fields[column_name] = formatted_value
            print(f"[update] ✏️ Using '{attr_id}' as update field: {formatted_value}")

    # Special fallback: if no lookup fields were found but we have leave_id or similar, use it
    if not lookup_fields:
        print("[update] ⚠️ No lookup fields found, trying fallback to primary key fields")
        primary_key_columns = ["leave_id", "id", "employee_id"]
        
        for col in primary_key_columns:
            # Try to find in data keys that map to these columns
            for attr_id, value in data.items():
                attr_id_lower = attr_id.lower()
                if attr_id_lower in attr_map:
                    column = attr_map[attr_id_lower]
                    if column.lower() == col.lower():
                        # Format the value based on data type
                        data_type = type_map.get(attr_id_lower, 'string')
                        formatted_value = value
                        if data_type == 'integer' and not isinstance(value, int):
                            try:
                                formatted_value = int(value)
                            except (ValueError, TypeError):
                                formatted_value = str(value)
                        
                        lookup_fields[column] = formatted_value
                        print(f"[update] ✅ Fallback: Using '{attr_id}' → '{column}' as lookup field")
                        break
            
            # If we found at least one lookup field, we can stop searching
            if lookup_fields:
                break

    if not lookup_fields:
        raise ValueError("[update] No valid lookup keys found (metadata.usage = 'lookup' or 'both')")

    # Step 5: Fetch record ID based on lookup
    # CRITICAL FIX: Build the WHERE clause with explicit type casting
    where_conditions = []
    for col, val in lookup_fields.items():
        if isinstance(val, int):
            # For integers, cast the column to integer
            where_conditions.append(f"CAST({col} AS INTEGER) = :{col}")
        elif isinstance(val, float):
            # For floats, cast the column to numeric
            where_conditions.append(f"CAST({col} AS NUMERIC) = :{col}")
        else:
            # For strings and other types, use simple equality
            where_conditions.append(f"{col} = :{col}")
    
    where_clause = " AND ".join(where_conditions)
    query = f"SELECT id FROM workflow_runtime.{table} WHERE {where_clause} LIMIT 1"
    print("[update] 🔍 Executing row lookup query:\n", query)
    print("[update] 🔍 Bindings:", lookup_fields)

    row = db.execute(text(query), lookup_fields).fetchone()
    if not row:
        raise ValueError("[update] No matching row found to update")

    record_id = row.id

    # Step 6: Finalize update set
    if not update_fields:
        return {"status": "Nothing to update", "id": record_id}

    set_clause = ", ".join([f"{col} = :{col}" for col in update_fields])
    update_query = f"""
        UPDATE workflow_runtime.{table}
        SET {set_clause}
        WHERE id = :record_id
    """
    update_fields["record_id"] = record_id

    print(f"[update] ✅ Executing UPDATE on workflow_runtime.{table}")
    print("[update] SQL:", update_query)
    print("[update] Bindings:", update_fields)

    db.execute(text(update_query), update_fields)
    db.commit()
    return {"status": "updated", "id": record_id}

# Data Validation Functions

@function_repository.register("validation", "validate_audit_fields")
def validate_audit_fields(*args, **kwargs) -> dict:
    print("📥 Input kwargs to validate_audit_fields:", kwargs)

    user_id = str(kwargs.get("user_id", "")).strip('"')
    timestamp = str(kwargs.get("timestamp", "")).strip('"')
    action = str(kwargs.get("action", "")).strip('"')
    reason = str(kwargs.get("reason_for_change", "")).strip('"')

    if not user_id:
        return {"is_valid": False, "message": "Missing required field: user_id"}

    if not timestamp or " " not in timestamp:
        return {"is_valid": False, "message": "Invalid timestamp format. Expected 'YYYY-MM-DD HH:MM:SS'"}

    if action not in ["CREATE", "UPDATE", "DELETE", "VIEW"]:
        return {"is_valid": False, "message": f"Invalid action: {action}"}

    if action in ["UPDATE", "DELETE"] and not reason:
        return {"is_valid": False, "message": f"Reason required for action: {action}"}

    return {"is_valid": True, "message": "Audit fields are valid"}

######################################################################
###New Function for validate Pattern (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("validation", "validate_pattern")
def validate_pattern(pattern: str, text: str, flags: int = 0) -> Dict[str, Any]:
    """
    Validate input text against a regular expression pattern.
    
    Args:
        pattern (str): Regular expression pattern
        text (str): Text to validate
        flags (int, optional): Regex flags (e.g., re.IGNORECASE)
        
    Returns:
        Dict[str, Any]: Validation result
    """
    try:
        is_valid = bool(re.match(pattern, text, flags))
        return {
            "is_valid": is_valid,
            "message": "Pattern matched" if is_valid else "Pattern did not match"
        }
    except re.error as e:
        return {
            "is_valid": False,
            "message": f"Invalid regex pattern: {str(e)}"
        }

########################################################################

######################################################################
###New Function for validate Complex (To be tested)
###Date 7/5/25
######################################################################


@function_repository.register("validation", "validate_complex")
def validate_complex(value: Dict[str, Any], rules: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate data against multiple rules with custom logic.
    
    Args:
        value (Dict[str, Any]): Data to validate
        rules (Dict[str, Any]): Validation rules for each field
        
    Returns:
        Dict[str, Any]: Validation results for each field
    """
    results = {}
    all_valid = True
    
    for field, field_rules in rules.items():
        field_value = value.get(field)
        field_results = []
        
        for rule in field_rules:
            rule_name = rule
            rule_params = {}
            
            # Handle rules with parameters like "min:18"
            if ":" in rule:
                rule_parts = rule.split(":", 1)
                rule_name = rule_parts[0]
                rule_params_str = rule_parts[1]
                
                # Try to parse parameters
                try:
                    # Handle numeric parameters
                    if rule_params_str.isdigit():
                        rule_params = {"value": int(rule_params_str)}
                    # Handle comma-separated parameters
                    elif "," in rule_params_str:
                        params_list = rule_params_str.split(",")
                        rule_params = {"values": params_list}
                    # Default string parameter
                    else:
                        rule_params = {"value": rule_params_str}
                except Exception:
                    rule_params = {"value": rule_params_str}
            
            # Execute appropriate validation function
            try:
                result = None
                if rule_name == "required":
                    result = function_repository.auto_execute("validate_required", field_value)
                elif rule_name == "email":
                    result = function_repository.auto_execute("validate_email", field_value)
                elif rule_name == "pattern":
                    result = validate_pattern(rule_params.get("value", ""), field_value)
                elif rule_name == "min":
                    min_value = rule_params.get("value", 0)
                    result = function_repository.auto_execute("compare", a=field_value, b=min_value, operator=">=")
                elif rule_name == "max":
                    max_value = rule_params.get("value", 0)
                    result = function_repository.auto_execute("compare", a=field_value, b=max_value, operator="<=")
                elif rule_name == "enum":
                    allowed_values = rule_params.get("values", [])
                    result = function_repository.auto_execute("enum_check", value=field_value, allowed_values=allowed_values)
                else:
                    result = {"is_valid": False, "message": f"Unknown validation rule: {rule_name}"}
                
                if result:
                    field_results.append({
                        "rule": rule,
                        "is_valid": result.get("is_valid", False),
                        "message": result.get("message", "")
                    })
                    
                    if not result.get("is_valid", False):
                        all_valid = False
            except Exception as e:
                field_results.append({
                    "rule": rule,
                    "is_valid": False,
                    "message": f"Error executing validation: {str(e)}"
                })
                all_valid = False
        
        results[field] = field_results
    
    return {
        "is_valid": all_valid,
        "fields": results,
        "message": "All validations passed" if all_valid else "One or more validations failed"
    }

##################################################################################

######################################################################
###New Function for validate Batch (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("validation", "validate_batch")
def validate_batch(values: Dict[str, Any], rules: Dict[str, str]) -> Dict[str, Any]:
    """
    Validate multiple values in a single operation with detailed results.
    
    Args:
        values (Dict[str, Any]): Dictionary of values to validate
        rules (Dict[str, str]): Dictionary mapping field names to validation rules
        
    Returns:
        Dict[str, Any]: Validation results for each field
    """
    results = {}
    all_valid = True
    
    for field, rule in rules.items():
        value = values.get(field)
        
        # Execute appropriate validation function
        try:
            result = None
            if rule == "required":
                result = function_repository.auto_execute("validate_required", value)
            elif rule == "email":
                result = function_repository.auto_execute("validate_email", value)
            elif rule.startswith("pattern:"):
                pattern = rule[8:]  # Remove "pattern:" prefix
                result = validate_pattern(pattern, value)
            elif rule.startswith("min:"):
                min_value = int(rule[4:])  # Remove "min:" prefix
                result = function_repository.auto_execute("compare", a=value, b=min_value, operator=">=")
            elif rule.startswith("max:"):
                max_value = int(rule[4:])  # Remove "max:" prefix
                result = function_repository.auto_execute("compare", a=value, b=max_value, operator="<=")
            elif rule.startswith("enum:"):
                allowed_values = rule[5:].split(",")  # Remove "enum:" prefix and split by comma
                result = function_repository.auto_execute("enum_check", value=value, allowed_values=allowed_values)
            else:
                result = {"is_valid": False, "message": f"Unknown validation rule: {rule}"}
            
            if result:
                results[field] = {
                    "rule": rule,
                    "is_valid": result.get("is_valid", False),
                    "message": result.get("message", "")
                }
                
                if not result.get("is_valid", False):
                    all_valid = False
        except Exception as e:
            results[field] = {
                "rule": rule,
                "is_valid": False,
                "message": f"Error executing validation: {str(e)}"
            }
            all_valid = False
    
    return {
        "is_valid": all_valid,
        "fields": results,
        "message": "All validations passed" if all_valid else "One or more validations failed"
    }


###########################################################################

######################################################################
###New Function for validate Date Range (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("validation", "validate_date_range")
def validate_date_range(date_value: str, start_date: str, end_date: str, inclusive: bool = True) -> Dict[str, Any]:
    """
    Validate that a date falls within a specified range.
    
    Args:
        date_value (str): Date to validate in ISO format (YYYY-MM-DD)
        start_date (str): Start date of valid range
        end_date (str): End date of valid range
        inclusive (bool, optional): Whether the range is inclusive. Defaults to True.
        
    Returns:
        Dict[str, Any]: Validation result
    """
    fmt = "%Y-%m-%d"
    try:
        # Parse dates
        date_obj = datetime.datetime.strptime(date_value, fmt).date()
        start_obj = datetime.datetime.strptime(start_date, fmt).date()
        end_obj = datetime.datetime.strptime(end_date, fmt).date()
        
        # Validate range
        if inclusive:
            is_valid = start_obj <= date_obj <= end_obj
        else:
            is_valid = start_obj < date_obj < end_obj
        
        return {
            "is_valid": is_valid,
            "message": f"Date {'is' if is_valid else 'is not'} within the specified range"
        }
    except ValueError as e:
        return {
            "is_valid": False,
            "message": f"Invalid date format: {str(e)}"
        }
    
###########################################################################    


@function_repository.register("validation", "validate_email")
def validate_email(email: str) -> Dict[str, Any]:
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    is_valid = bool(re.match(pattern, email))
    return {
        "is_valid": is_valid,
        "message": "Valid email format" if is_valid else "Invalid email format"
    }

@function_repository.register("validation", "validate_required")
def validate_required(value: Any) -> Dict[str, Any]:
    """Validate that a value is not None or empty string."""
    is_valid = value is not None and value != ""
    return {
        "is_valid": is_valid,
        "message": "Value is provided" if is_valid else "Value is required"
    }

@function_repository.register("validation", "enum_check")
def enum_check(*args, **kwargs) -> Dict[str, Any]:
    """
    Safely check if a value exists in the allowed enum list.
    Works even if 'value' is passed both positionally and in kwargs.
    """
    # Extract `value` from kwargs only
    actual_value = kwargs.get("value")
    allowed_values = kwargs.get("allowed_values")

    if actual_value is None or allowed_values is None:
        raise ValueError("Missing 'value' or 'allowed_values' in parameters.")

    is_valid = str(actual_value).lower() in [v.lower() for v in allowed_values]

    return {
        "is_valid": is_valid,
        "message": "Valid enum value." if is_valid else f"Invalid value '{actual_value}'. Allowed: {allowed_values}"
    }

@function_repository.register("validation", "entity_exists")
def entity_exists(db: Session, entity: str, attribute: str, value: Any) -> Dict[str, Any]:
    table = db.execute(
        text("SELECT name FROM workflow_runtime.entities WHERE entity_id = :entity"),
        {"entity": entity}
    ).scalar()

    col = db.execute(
        text("SELECT name FROM workflow_runtime.entity_attributes WHERE entity_id = :entity AND attribute_id = :attribute"),
        {"entity": entity, "attribute": attribute}
    ).scalar()

    if not table or not col:
        return {
            "is_valid": False,
            "message": f"Entity or attribute not found: {entity}.{attribute}"
        }

    query = text(f"SELECT 1 FROM workflow_runtime.z_entity_{table.lower()} WHERE {col.lower()} = :val LIMIT 1")
    exists = bool(db.execute(query, {"val": value}).fetchone())

    return {
        "is_valid": exists,
        "message": "Entity exists" if exists else f"No record found for {col} = {value}"
    }


@function_repository.register("validation", "compare")
def compare(db: Optional[Session] = None, *, a: Any, b: Any, operator: str = "==", **kwargs) -> Dict[str, Any]:
    ops = {
        "==": lambda x, y: x == y,
        "!=": lambda x, y: x != y,
        ">": lambda x, y: x > y,
        ">=": lambda x, y: x >= y,
        "<": lambda x, y: x < y,
        "<=": lambda x, y: x <= y,
        "greater_than_or_equal": lambda x, y: x >= y,
        "less_than_or_equal": lambda x, y: x <= y
    }

    if operator not in ops:
        return {
            "is_valid": False,
            "message": f"Unsupported operator: {operator}"
        }
    
    # Try to convert both values to same type for proper comparison
    try:
        # If both can be converted to numbers, do that
        if isinstance(a, (int, float)) or (isinstance(a, str) and a.replace('.', '', 1).isdigit()):
            if isinstance(b, str) and b.replace('.', '', 1).isdigit():
                # Convert b to match a's type (int or float)
                if isinstance(a, int):
                    b = int(b)
                else:
                    b = float(b)
            elif isinstance(b, (int, float)):
                #for attr_id, value in data.items():
    
                if isinstance(a, int) and isinstance(b, float):
                    a = float(a)
                elif isinstance(a, float) and isinstance(b, int):
                    b = float(b)
        
        # If both are strings, keep as is - string comparison is fine
        print(f"DEBUG: Compare - Type conversion: a={a} ({type(a)}), b={b} ({type(b)})")
        
        result = ops[operator](a, b)

        return {
            "is_valid": result,
            "message": f"Validation {'passed' if result else 'failed'}: {a} {operator} {b}"
        }
    except Exception as e:
        print(f"ERROR in compare function: {str(e)}")
        return {
            "is_valid": False,
            "message": f"Comparison error: {str(e)}"
        }


# Data Transformation Functions
# @function_repository.register("transform", "format_date")
# def format_date(date_value: Union[str, datetime.datetime], format_string: str = "%Y-%m-%d") -> str:
#     """Format a date string or datetime object to the specified format."""
#     if isinstance(date_value, str):
#         date_obj = datetime.datetime.fromisoformat(date_value.replace("Z", "+00:00"))
#     elif isinstance(date_value, datetime.datetime):
#         date_obj = date_value
#     else:
#         raise ValueError("date_value must be a string or datetime object")
    
#     return date_obj.strftime(format_string)

#############################################################
#Number_format function     To be test 07/05/2025
#############################################################

@function_repository.register("transform", "number_format")
def number_format(
    number: Union[int, float],
    decimal_places: int = 2,
    thousands_sep: str = ",",
    decimal_sep: str = "."
) -> str:
    """
    Format numbers with locale and presentation options.
    
    Args:
        number (Union[int, float]): Number to format
        decimal_places (int, optional): Number of decimal places. Defaults to 2.
        thousands_sep (str, optional): Thousands separator. Defaults to ",".
        decimal_sep (str, optional): Decimal separator. Defaults to ".".
        
    Returns:
        str: Formatted number string
    """
    try:
        # Round to specified decimal places
        rounded = round_number(number, decimal_places)
        
        # Split number into integer and decimal parts
        integer_part, decimal_part = str(rounded).split(".")
        
        # Add leading zeros to decimal part if needed
        decimal_part = decimal_part.ljust(decimal_places, "0")
        
        # Add thousands separators to integer part
        formatted_integer = ""
        for i, digit in enumerate(reversed(integer_part)):
            if i > 0 and i % 3 == 0:
                formatted_integer = thousands_sep + formatted_integer
            formatted_integer = digit + formatted_integer
        
        # Combine parts with decimal separator
        if decimal_places > 0:
            return f"{formatted_integer}{decimal_sep}{decimal_part}"
        else:
            return formatted_integer
    except Exception as e:
        raise ValueError(f"Error formatting number: {str(e)}")
############################################################################# 

#############################################################
#String Templete function     To be test 07/05/2025
#############################################################

@function_repository.register("transform", "string_template")
def string_template(template: str, data: Dict[str, Any]) -> str:
    """
    Populate a string template with variable values.
    
    Args:
        template (str): Template string with placeholders like {{variable}}
        data (Dict[str, Any]): Dictionary of variables to substitute
        
    Returns:
        str: Populated template string
    """
    # Regular expression to find template variables
    pattern = r"{{(\w+)}}"
    
    # Replace each match with corresponding value
    def replace_var(match):
        var_name = match.group(1)
        if var_name in data:
            return str(data[var_name])
        return match.group(0)  # Keep original if variable not found
    
    return re.sub(pattern, replace_var, template)

##################################################################

#############################################################
#Format Currency function     To be test 07/05/2025
#############################################################

@function_repository.register("transform", "format_currency")
def format_currency(
    amount: Union[int, float],
    currency: str = "USD",
    decimal_places: int = 2,
    locale: str = "en_US"
) -> str:
    """
    Format numeric values as currency.
    
    Args:
        amount (Union[int, float]): Amount to format
        currency (str, optional): Currency code. Defaults to "USD".
        decimal_places (int, optional): Number of decimal places. Defaults to 2.
        locale (str, optional): Locale for formatting. Defaults to "en_US".
        
    Returns:
        str: Formatted currency string
    """
    # Dictionary of currency symbols and formatting rules
    currency_info = {
        "USD": {"symbol": "$", "position": "prefix"},
        "EUR": {"symbol": "€", "position": "prefix"},
        "GBP": {"symbol": "£", "position": "prefix"},
        "JPY": {"symbol": "¥", "position": "prefix"},
        "CNY": {"symbol": "¥", "position": "prefix"},
        "INR": {"symbol": "₹", "position": "prefix"},
        "CAD": {"symbol": "CA$", "position": "prefix"},
        "AUD": {"symbol": "A$", "position": "prefix"},
        "BRL": {"symbol": "R$", "position": "prefix"},
    }
    
    # Get currency info, default to currency code if not in dictionary
    info = currency_info.get(currency, {"symbol": currency, "position": "suffix"})
    symbol = info["symbol"]
    position = info["position"]
    
    # Format number
    formatted_number = number_format(amount, decimal_places)
    
    # Add currency symbol
    if position == "prefix":
        return f"{symbol}{formatted_number}"
    else:
        return f"{formatted_number} {symbol}"
    
##################################################################    


@function_repository.register("math", "subtract_days")
def subtract_days(db: Optional[Session] = None, *, start_date: str, end_date: str, **kwargs) -> int:
    import re
    unresolved = re.compile(r"\{\{.*?\}\}")
    
    # ✅ Defensive check to fail fast if placeholder wasn't resolved
    if unresolved.search(start_date) or unresolved.search(end_date):
        raise ValueError(f"🚨 Placeholder detected: start_date={start_date}, end_date={end_date}")

    fmt = "%Y-%m-%d"
    start = datetime.datetime.strptime(start_date, fmt)
    end = datetime.datetime.strptime(end_date, fmt)
    return (end - start).days



@function_repository.register("math", "add_days")
def add_days(db: Optional[Session] = None, *, base_date: str, days: int, **kwargs) -> str:
    fmt = "%Y-%m-%d"
    dt = datetime.datetime.strptime(base_date, fmt)
    return (dt + datetime.timedelta(days=days)).strftime(fmt)

# @function_repository.register("transform", "to_uppercase")
# def to_uppercase(text: str = "", *args, **kwargs) -> str:
#     return text.upper()

######################################################################
###New Function for Random Number (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("math", "round_number")
def round_number(number: Union[int, float, str], decimal_places: int = 0) -> float:
    """
    Round a number to specified decimal places.
    
    Args:
        number (Union[int, float, str]): Number to round
        decimal_places (int, optional): Number of decimal places. Defaults to 0.
        
    Returns:
        float: Rounded number
    """
    # Convert to Decimal for precision
    try:
        if isinstance(number, str):
            num = Decimal(number)
        else:
            num = Decimal(str(number))
        
        # Round using Decimal
        rounded = num.quantize(Decimal('0.1') ** decimal_places, rounding=ROUND_HALF_UP)
        return float(rounded)
    except Exception as e:
        raise ValueError(f"Error rounding number: {str(e)}")

##########################################################################


######################################################################
###New Function for Calculate Percentage (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("math", "calculate_percentage")
def calculate_percentage(part: Union[int, float], whole: Union[int, float], decimal_places: int = 2) -> float:
    """
    Calculate percentage of one number relative to another.
    
    Args:
        part (Union[int, float]): Part value (numerator)
        whole (Union[int, float]): Whole value (denominator)
        decimal_places (int, optional): Number of decimal places. Defaults to 2.
        
    Returns:
        float: Percentage value
    """
    if whole == 0:
        raise ValueError("Cannot calculate percentage with zero denominator")
    
    percentage = (part / whole) * 100
    return round_number(percentage, decimal_places)

#############################################################################

######################################################################
###New Function for Min_Max  (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("math", "min_max")
def min_max(values: List[Union[int, float]], operation: str = "min") -> Union[int, float]:
    """
    Find minimum or maximum value in a set of numbers.
    
    Args:
        values (List[Union[int, float]]): List of numeric values
        operation (str, optional): Operation to perform ("min" or "max"). Defaults to "min".
        
    Returns:
        Union[int, float]: Minimum or maximum value
    """
    if not values:
        raise ValueError("Cannot find min/max of empty list")
    
    # Convert string values to numbers if needed
    numeric_values = []
    for val in values:
        if isinstance(val, str):
            try:
                numeric_values.append(float(val))
            except ValueError:
                raise ValueError(f"Non-numeric value in list: {val}")
        else:
            numeric_values.append(val)
    
    operation = operation.lower()
    if operation == "min":
        return min(numeric_values)
    elif operation == "max":
        return max(numeric_values)
    else:
        raise ValueError(f"Invalid operation: {operation}. Use 'min' or 'max'.")
    
#################################################################################

######################################################################
###New Function for Average  (To be tested)
###Date 7/5/25
######################################################################

@function_repository.register("math", "average")
def average(values: List[Union[int, float]], decimal_places: int = 2) -> float:
    """
    Calculate average/mean of a set of values.
    
    Args:
        values (List[Union[int, float]]): List of numeric values
        decimal_places (int, optional): Number of decimal places. Defaults to 2.
        
    Returns:
        float: Average value
    """
    if not values:
        raise ValueError("Cannot calculate average of empty list")
    
    # Convert string values to numbers if needed
    numeric_values = []
    for val in values:
        if isinstance(val, str):
            try:
                numeric_values.append(float(val))
            except ValueError:
                raise ValueError(f"Non-numeric value in list: {val}")
        else:
            numeric_values.append(val)
    
    avg = sum(numeric_values) / len(numeric_values)
    return round_number(avg, decimal_places)

#################################################################



@function_repository.register("transform", "to_uppercase")
def to_uppercase(*args, text: str = "", **kwargs) -> str:
    """
    Convert the given text to uppercase.

    Args:
        text (str): The text to convert to uppercase.

    Returns:
        str: The uppercase version of the text.
    """
    # Handle cases where 'text' might be passed in kwargs or args
    if not text and "text" in kwargs:
        text = kwargs["text"]
    return text.upper()




@function_repository.register("transform", "to_lowercase")
def to_lowercase(text: str = "", *args, **kwargs) -> str:
    return text.lower()

@function_repository.register("transform", "format_date")
def format_date(*, date_value: Union[str, datetime.datetime], format_string: str = "%Y-%m-%d", **kwargs) -> str:
    if isinstance(date_value, str):
        date_obj = datetime.datetime.fromisoformat(date_value.replace("Z", "+00:00"))
    elif isinstance(date_value, datetime.datetime):
        date_obj = date_value
    else:
        raise ValueError("date_value must be a string or datetime object")
    
    return date_obj.strftime(format_string)


@function_repository.register("transform", "to_json")
def to_json(*, data: Any, **kwargs) -> str:
    return json.dumps(data)



# Mathematical Functions
@function_repository.register("math", "add")
def add(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """Add two numbers."""
    return a + b

@function_repository.register("math", "subtract")
def subtract(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """Subtract b from a."""
    return a - b

@function_repository.register("math", "multiply")
def multiply(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """Multiply two numbers."""
    return a * b

@function_repository.register("math", "divide")
def divide(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """Divide a by b."""
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b

# Data Manipulation Functions
@function_repository.register("data", "merge_dicts")
def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Merge two dictionaries."""
    result = dict1.copy()
    result.update(dict2)
    return result

@function_repository.register("data", "filter_dict")
def filter_dict(data: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
    """Filter dictionary to include only specified keys."""
    return {k: v for k, v in data.items() if k in keys}


############################################################
# Deep Merge To be test on 07/05/2025

############################################################

@function_repository.register("data", "deep_merge")
def deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Recursively merge nested dictionaries/objects.
    
    Args:
        dict1 (Dict[str, Any]): First dictionary
        dict2 (Dict[str, Any]): Second dictionary (takes precedence for common keys)
        
    Returns:
        Dict[str, Any]: Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            # Recursively merge nested dictionaries
            result[key] = deep_merge(result[key], value)
        else:
            # Replace or add value
            result[key] = value
    
    return result

#################################################################

############################################################
# Array Functions Merge To be test on 07/05/2025

############################################################

@function_repository.register("data", "array_functions")
def array_functions(
    array: List[Any],
    operation: str,
    key: str = None,
    value: Any = None
) -> Union[List[Any], Any]:
    """
    Perform operations on arrays (filter, map, sort, unique).
    
    Args:
        array (List[Any]): Input array
        operation (str): Operation to perform (filter, map, sort, unique, count, find)
        key (str, optional): Key/attribute to operate on (for dictionaries/objects)
        value (Any, optional): Value to filter or find
        
    Returns:
        Union[List[Any], Any]: Result of operation
    """
    if not array:
        return [] if operation != "count" else 0
    
    operation = operation.lower()
    
    # Handle unique operation
    if operation == "unique":
        if key and isinstance(array[0], dict):
            # Get unique values for a specific key in dict array
            values = [item.get(key) for item in array]
            unique_values = []
            seen = set()
            for val in values:
                # Handle unhashable types like dicts
                val_str = str(val)
                if val_str not in seen:
                    seen.add(val_str)
                    unique_values.append(val)
            return unique_values
        else:
            # Get unique values in list
            unique_items = []
            seen = set()
            for item in array:
                # Handle unhashable types
                item_str = str(item)
                if item_str not in seen:
                    seen.add(item_str)
                    unique_items.append(item)
            return unique_items
    
    # Handle filter operation
    elif operation == "filter":
        if key and value is not None:
            # Filter dicts by key value
            if isinstance(array[0], dict):
                return [item for item in array if item.get(key) == value]
            else:
                raise ValueError("Cannot filter by key on non-dict array")
        else:
            # Remove None and empty values
            return [item for item in array if item]
    
    # Handle sort operation
    elif operation == "sort":
        if key and isinstance(array[0], dict):
            # Sort dicts by key
            return sorted(array, key=lambda x: x.get(key))
        else:
            # Simple sort
            try:
                return sorted(array)
            except TypeError:
                return array  # Return unsorted for non-sortable items
    
    # Handle map operation
    elif operation == "map" and key:
        if isinstance(array[0], dict):
            # Extract specific key from dicts
            return [item.get(key) for item in array]
        else:
            raise ValueError("Cannot map by key on non-dict array")
    
    # Handle count operation
    elif operation == "count":
        if key and value is not None and isinstance(array[0], dict):
            # Count occurrences of value in specific key
            return sum(1 for item in array if item.get(key) == value)
        else:
            # Count total items
            return len(array)
    
    # Handle find operation
    elif operation == "find":
        if key and value is not None and isinstance(array[0], dict):
            # Find first item matching key/value
            for item in array:
                if item.get(key) == value:
                    return item
            return None
        else:
            # Find first occurrence of value
            for item in array:
                if item == value:
                    return item
            return None
    
    else:
        raise ValueError(f"Unsupported operation: {operation}")
#######################################################################

############################################################
# Parse Csv To be test on 07/05/2025

############################################################


@function_repository.register("data", "parse_csv")
def parse_csv(
    csv_data: str,
    delimiter: str = ",",
    has_header: bool = True,
    skip_rows: int = 0
) -> List[Dict[str, Any]]:
    """
    Parse CSV data into structured format.
    
    Args:
        csv_data (str): CSV data string
        delimiter (str, optional): Column delimiter. Defaults to ",".
        has_header (bool, optional): Whether CSV has header row. Defaults to True.
        skip_rows (int, optional): Number of rows to skip. Defaults to 0.
        
    Returns:
        List[Dict[str, Any]]: Parsed CSV data
    """
    lines = csv_data.strip().split("\n")
    
    # Skip rows if requested
    if skip_rows > 0:
        lines = lines[skip_rows:]
    
    if not lines:
        return []
    
    # Parse header if present
    headers = None
    if has_header:
        header_line = lines[0]
        headers = [h.strip() for h in header_line.split(delimiter)]
        data_lines = lines[1:]
    else:
        # Generate column indices as headers
        first_line = lines[0]
        column_count = len(first_line.split(delimiter))
        headers = [f"column{i}" for i in range(column_count)]
        data_lines = lines
    
    # Parse data rows
    result = []
    for line in data_lines:
        if not line.strip():
            continue
            
        values = [v.strip() for v in line.split(delimiter)]
        
        # Handle case where values length doesn't match headers
        if len(values) < len(headers):
            values.extend([""] * (len(headers) - len(values)))
        elif len(values) > len(headers):
            values = values[:len(headers)]
        
        # Convert values to appropriate types
        typed_values = []
        for value in values:
            if value == "":
                typed_values.append(None)
            elif value.lower() == "true":
                typed_values.append(True)
            elif value.lower() == "false":
                typed_values.append(False)
            elif value.isdigit():
                typed_values.append(int(value))
            elif re.match(r"^-?\d+\.\d+$", value):
                typed_values.append(float(value))
            else:
                typed_values.append(value)
        
        row_dict = dict(zip(headers, typed_values))
        result.append(row_dict)
    
    return result

####################################################################

############################################################
# Extract json field To be test on 07/05/2025

############################################################

@function_repository.register("data", "extract_json_field")
def extract_json_field(json_data: Union[str, Dict[str, Any]], path: str) -> Any:
    """
    Extract specific fields from JSON data.
    
    Args:
        json_data (Union[str, Dict[str, Any]]): JSON string or object
        path (str): Dot notation path to extract (e.g., "user.address.city")
        
    Returns:
        Any: Extracted value or None if not found
    """
    # Parse JSON if string
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON data: {str(e)}")
    else:
        data = json_data
    
    # Split path into parts
    parts = path.split(".")
    
    # Traverse the JSON structure
    current = data
    for part in parts:
        # Handle arrays with index
        if "[" in part and part.endswith("]"):
            array_name, index_str = part.split("[", 1)
            index = int(index_str[:-1])  # Remove closing bracket
            
            if array_name not in current:
                return None
                
            array = current[array_name]
            if not isinstance(array, list) or index >= len(array):
                return None
                
            current = array[index]
        # Handle normal object properties
        elif part in current:
            current = current[part]
        else:
            return None
    
    return current

#################################################################

############################################################
# data transform To be test on 07/05/2025

############################################################

@function_repository.register("data", "data_transform")
def data_transform(
    data: Any,
    transform_type: str,
    options: Dict[str, Any] = None
) -> Any:
    """
    Transform data structure from one format to another.
    
    Args:
        data (Any): Input data to transform
        transform_type (str): Type of transformation to perform
        options (Dict[str, Any], optional): Options for transformation. Defaults to None.
        
    Returns:
        Any: Transformed data
    """
    if options is None:
        options = {}
    
    transform_type = transform_type.lower()
    
    # Array to key-value object transformation
    if transform_type == "array_to_object":
        if not isinstance(data, list):
            raise ValueError("Data must be an array for array_to_object transformation")
            
        key_field = options.get("key_field")
        if not key_field:
            raise ValueError("key_field option is required for array_to_object transformation")
            
        result = {}
        for item in data:
            if isinstance(item, dict) and key_field in item:
                result[str(item[key_field])] = item
        return result
    
    # Key-value object to array transformation
    elif transform_type == "object_to_array":
        if not isinstance(data, dict):
            raise ValueError("Data must be an object for object_to_array transformation")
            
        key_field = options.get("key_field", "key")
        value_field = options.get("value_field", "value")
            
        if value_field == "__self__":
            # Keep object values as is
            return [{"key": k, "value": v} for k, v in data.items()]
        else:
            # Store key in each object
            result = []
            for key, value in data.items():
                if isinstance(value, dict):
                    value[key_field] = key
                    result.append(value)
                else:
                    result.append({key_field: key, value_field: value})
            return result
    
    # Flatten nested objects
    elif transform_type == "flatten":
        if not isinstance(data, dict):
            raise ValueError("Data must be an object for flatten transformation")
            
        delimiter = options.get("delimiter", ".")
        
        def flatten_recursive(d, parent_key=""):
            items = []
            for key, value in d.items():
                new_key = f"{parent_key}{delimiter}{key}" if parent_key else key
                
                if isinstance(value, dict):
                    items.extend(flatten_recursive(value, new_key).items())
                else:
                    items.append((new_key, value))
            return dict(items)
        
        return flatten_recursive(data)
    
    # Pivot data (similar to Excel pivot tables)
    elif transform_type == "pivot":
        if not isinstance(data, list):
            raise ValueError("Data must be an array for pivot transformation")
            
        row_field = options.get("row_field")
        column_field = options.get("column_field")
        value_field = options.get("value_field")
        
        if not row_field or not column_field or not value_field:
            raise ValueError("row_field, column_field, and value_field options are required for pivot transformation")
        
        result = {}
        for item in data:
            row_value = item.get(row_field)
            column_value = item.get(column_field)
            data_value = item.get(value_field)
            
            if row_value is None or column_value is None:
                continue
                
            if row_value not in result:
                result[row_value] = {}
                
            result[row_value][column_value] = data_value
        
        return result
    
    # Transform array of objects to array of arrays (like CSV)
    elif transform_type == "to_table":
        if not isinstance(data, list) or not all(isinstance(item, dict) for item in data):
            raise ValueError("Data must be an array of objects for to_table transformation")
            
        include_header = options.get("include_header", True)
        columns = options.get("columns", None)
        
        if not columns:
            # Determine columns from data
            columns = []
            for item in data:
                for key in item.keys():
                    if key not in columns:
                        columns.append(key)
        
        result = []
        if include_header:
            result.append(columns)
            
        for item in data:
            row = [item.get(col) for col in columns]
            result.append(row)
            
        return result
    
    else:
        raise ValueError(f"Unsupported transformation type: {transform_type}")
    
################################################################################

############################################################
# Group by aggregate To be test on 07/05/2025

############################################################

@function_repository.register("data", "groupby_aggregate")
def groupby_aggregate(
    data: List[Dict[str, Any]],
    group_by: Union[str, List[str]],
    aggregate_field: str,
    aggregation: str = "sum"
) -> Dict[str, Any]:
    """
    Group data by key and perform aggregations.
    
    Args:
        data (List[Dict[str, Any]]): Array of objects to group
        group_by (Union[str, List[str]]): Field(s) to group by
        aggregate_field (str): Field to aggregate
        aggregation (str, optional): Aggregation type (sum, avg, min, max, count). Defaults to "sum".
        
    Returns:
        Dict[str, Any]: Grouped and aggregated data
    """
    if not data:
        return {}
    
    # Convert single group_by field to list
    if isinstance(group_by, str):
        group_by_fields = [group_by]
    else:
        group_by_fields = group_by
    
    # Initialize result
    result = {}
    
    # Group data
    for item in data:
        # Create group key
        if len(group_by_fields) == 1:
            group_key = item.get(group_by_fields[0])
            if group_key is None:
                continue
            group_key = str(group_key)
        else:
            # Create compound key for multiple fields
            key_parts = []
            for field in group_by_fields:
                value = item.get(field)
                if value is None:
                    break
                key_parts.append(str(value))
                
            if len(key_parts) != len(group_by_fields):
                continue
                
            group_key = "|".join(key_parts)
        
        # Get value to aggregate
        value = item.get(aggregate_field)
        if value is None:
            continue
            
        # Try to convert value to number if needed
        if aggregation != "count":
            try:
                if isinstance(value, str):
                    value = float(value)
            except ValueError:
                continue
        
        # Add to result
        if group_key not in result:
            if aggregation == "count":
                result[group_key] = 1
            else:
                result[group_key] = value
        else:
            if aggregation == "sum" or aggregation == "count":
                result[group_key] += 1 if aggregation == "count" else value
            elif aggregation == "min":
                result[group_key] = min(result[group_key], value)
            elif aggregation == "max":
                result[group_key] = max(result[group_key], value)
            elif aggregation == "avg":
                # For average, we need to track sum and count
                if isinstance(result[group_key], dict):
                    result[group_key]["sum"] += value
                    result[group_key]["count"] += 1
                else:
                    result[group_key] = {
                        "sum": result[group_key] + value,
                        "count": 2
                    }
    
    # Calculate averages if needed
    if aggregation == "avg":
        for key, value in result.items():
            if isinstance(value, dict):
                result[key] = value["sum"] / value["count"]
    
    # Add group_by fields to result if multiple fields
    if len(group_by_fields) > 1:
        detailed_result = {}
        for key, value in result.items():
            field_values = key.split("|")
            group_obj = {field: val for field, val in zip(group_by_fields, field_values)}
            group_obj[f"{aggregation}_{aggregate_field}"] = value
            detailed_result[key] = group_obj
        return detailed_result
    
    return result

#################################################################################


# Utility Functions


@function_repository.register("utility", "generate_id")
def generate_id(db: Session, entity: str, attribute: Any, **kwargs) -> str:
    """
    Generate next numeric ID based on the actual primary key column.
    - It checks whether the attribute exists in the DB.
    - If not found, it defaults to `id`, which all entity tables must have.
    """
    from services.database_utils import CaseConverter

    # Step 1: Get entity table name from the table_name field
    entity_result = db.execute(text("""
        SELECT name, table_name FROM workflow_runtime.entities WHERE entity_id = :entity_id
    """), {"entity_id": entity}).fetchone()

    if not entity_result:
        raise ValueError(f"[generate_id] Entity '{entity}' not found.")

    # Use table_name if available, otherwise fall back to the old format
    if entity_result.table_name:
        actual_table_name = entity_result.table_name
    else:
        # Fallback to old format
        table_snake = CaseConverter.to_snake_case(entity_result.name)
        actual_table_name = f"e_{table_snake}"

    # Step 2: Try resolving the column name from attribute_id
    # Ensure attribute is treated as a string, regardless of what type it was passed as
    attribute_str = str(attribute)
    
    # Debug the attribute value
    print(f"DEBUG: generate_id received attribute={attribute} (type: {type(attribute)}), converted to {attribute_str}")
    
    # Check if we're dealing with a numeric attribute that should be an attribute ID
    if attribute_str.isdigit() or (isinstance(attribute, int)):
        # Try to find an attribute ID in the database that contains this numeric value
        try:
            # Look for attribute IDs that contain this number
            numeric_value = str(attribute).strip()
            lookup_query = text("""
                SELECT attribute_id FROM workflow_runtime.entity_attributes
                WHERE entity_id = :entity AND attribute_id LIKE :pattern
                LIMIT 1
            """)
            
            # Try with different patterns (at031, at31, etc.)
            patterns = [
                f"%{numeric_value}%",  # Contains the number anywhere
                f"at{numeric_value}%", # Starts with at followed by the number
                f"at0{numeric_value}%", # Starts with at0 followed by the number
                f"at00{numeric_value}%" # Starts with at00 followed by the number
            ]
            
            for pattern in patterns:
                lookup_result = db.execute(lookup_query, {"entity": entity, "pattern": pattern}).fetchone()
                if lookup_result:
                    # Use the attribute ID from the database
                    attribute_str = lookup_result[0]
                    print(f"DEBUG: Found matching attribute ID in database: {attribute_str}")
                    break
            
            # If no match found, fall back to default format
            if not lookup_result:
                attribute_str = f"at{numeric_value.zfill(3)}"
                print(f"DEBUG: No matching attribute ID found, using default format: {attribute_str}")
        except Exception as e:
            print(f"DEBUG: Error looking up attribute ID: {str(e)}")
            # Fallback: Convert numeric value to attribute ID format
            attribute_str = f"at{attribute_str.zfill(3)}"
            print(f"DEBUG: Converted numeric attribute to {attribute_str}")
    
    # Try to get the column name using the resolved attribute ID
    column_name = db.execute(text("""
        SELECT name FROM workflow_runtime.entity_attributes
        WHERE entity_id = :entity AND attribute_id = :attribute
        """), {"entity": entity, "attribute": attribute_str}).scalar()

    if not column_name:
        # 🔁 Fallback: Try to find a column named 'id'
        column_name = db.execute(text("""
            SELECT name FROM workflow_runtime.entity_attributes
            WHERE entity_id = :entity AND LOWER(name) = 'id'
        """), {"entity": entity}).scalar()

        if not column_name:
            raise ValueError(f"[generate_id] Neither attribute_id '{attribute}' nor default 'id' column found for entity '{entity}'.")

    # Use the actual column name from the database without snake case conversion
    actual_column_name = column_name

    # Step 3: Query max value
    try:
        result = db.execute(text(f"""
            SELECT MAX(CAST({actual_column_name} AS INT)) FROM workflow_runtime.{actual_table_name}
        """)).scalar()
        next_id = (int(result or 0) + 1)
        return str(next_id)
    except Exception as e:
        raise RuntimeError(f"[generate_id] Failed on {actual_table_name}.{actual_column_name}: {e}")


# @function_repository.register("utility", "generate_id")
# def generate_id(prefix: str = "id") -> str:
#     """Generate a unique ID."""
#     import uuid
#     return f"{prefix}-{uuid.uuid4()}"

@function_repository.register("utility", "current_timestamp")
def current_timestamp(*args, **kwargs) -> str:
    """Get current ISO timestamp."""
    return datetime.datetime.now().isoformat()


@function_repository.register("utility", "notify")
def notify(*args, **kwargs) -> dict:
    """
    Simple notification function — logs or simulates a message send.
    """
    print("📣 Notify called with:", kwargs)
    return {"status": "Notified"}


########################################################################
#Generate Random      To be test 07/05/2025
########################################################################

@function_repository.register("utility", "generate_random")
def generate_random(
    type_str: str = "string",
    length: int = 8,
    min_value: int = 1,
    max_value: int = 100,
    options: Dict[str, Any] = None
) -> Any:
    """
    Generate random values (numbers, strings).
    
    Args:
        type_str (str, optional): Type of random value (string, number, uuid, boolean, date). Defaults to "string".
        length (int, optional): Length for strings. Defaults to 8.
        min_value (int, optional): Minimum value for numbers. Defaults to 1.
        max_value (int, optional): Maximum value for numbers. Defaults to 100.
        options (Dict[str, Any], optional): Additional options. Defaults to None.
        
    Returns:
        Any: Random value of specified type
    """
    if options is None:
        options = {}
    
    type_str = type_str.lower()
    
    if type_str == "string":
        chars = options.get("chars")
        if not chars:
            # Default character set based on options
            lowercase = options.get("lowercase", True)
            uppercase = options.get("uppercase", True)
            digits = options.get("digits", True)
            special = options.get("special", False)
            
            chars = ""
            if lowercase:
                chars += string.ascii_lowercase
            if uppercase:
                chars += string.ascii_uppercase
            if digits:
                chars += string.digits
            if special:
                chars += string.punctuation
            
            if not chars:
                chars = string.ascii_letters + string.digits
        
        return "".join(random.choice(chars) for _ in range(length))
    
    elif type_str == "number" or type_str == "integer":
        return random.randint(min_value, max_value)
    
    elif type_str == "decimal" or type_str == "float":
        return round(random.uniform(min_value, max_value), options.get("decimal_places", 2))
    
    elif type_str == "uuid":
        return str(uuid.uuid4())
    
    elif type_str == "boolean":
        return random.choice([True, False])
    
    elif type_str == "date":
        # Generate random date between min and max dates
        start_date = options.get("start_date")
        end_date = options.get("end_date")
        
        if not start_date:
            start_date = datetime.date(2000, 1, 1)
        elif isinstance(start_date, str):
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
        
        if not end_date:
            end_date = datetime.date.today()
        elif isinstance(end_date, str):
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        
        days_between = (end_date - start_date).days
        if days_between < 0:
            start_date, end_date = end_date, start_date
            days_between = abs(days_between)
        
        random_days = random.randint(0, days_between)
        random_date = start_date + datetime.timedelta(days=random_days)
        
        return random_date.strftime("%Y-%m-%d")
    
    else:
        raise ValueError(f"Unsupported random type: {type_str}")

####################################################################################

########################################################################
#Hash Data      To be test 07/05/2025
########################################################################

@function_repository.register("utility", "hash_data")
def hash_data(data: str, algorithm: str = "md5", encoding: str = "utf-8") -> str:
    """
    Create secure hash of sensitive data.
    
    Args:
        data (str): Data to hash
        algorithm (str, optional): Hash algorithm (md5, sha1, sha256, sha512). Defaults to "md5".
        encoding (str, optional): Data encoding. Defaults to "utf-8".
        
    Returns:
        str: Hashed data
    """
    if not data:
        return ""
    
    # Convert data to bytes if it's a string
    if isinstance(data, str):
        data_bytes = data.encode(encoding)
    else:
        data_bytes = data
    
    # Choose algorithm
    algorithm = algorithm.lower()
    if algorithm == "md5":
        hash_obj = hashlib.md5(data_bytes)
    elif algorithm == "sha1":
        hash_obj = hashlib.sha1(data_bytes)
    elif algorithm == "sha256":
        hash_obj = hashlib.sha256(data_bytes)
    elif algorithm == "sha512":
        hash_obj = hashlib.sha512(data_bytes)
    else:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")
    
    # Return hexadecimal digest
    return hash_obj.hexdigest()


#########################################################################

########################################################################
#Date Functions      To be test 07/05/2025
########################################################################

@function_repository.register("temporal", "date_functions")
def date_functions(
    date_value: str,
    operation: str,
    unit: str = "days",
    amount: int = 0,
    format_str: str = "%Y-%m-%d"
) -> Union[str, int]:
    """
    Comprehensive date manipulation beyond simple add/subtract.
    
    Args:
        date_value (str): Date string in ISO format (YYYY-MM-DD)
        operation (str): Operation to perform (format, add, subtract, diff, compare, extract)
        unit (str, optional): Time unit for operations (days, months, years). Defaults to "days".
        amount (int, optional): Amount to add/subtract. Defaults to 0.
        format_str (str, optional): Format string for output. Defaults to "%Y-%m-%d".
        
    Returns:
        Union[str, int]: Result of date operation
    """
    # Parse date
    if isinstance(date_value, str):
        try:
            date_obj = datetime.datetime.strptime(date_value, "%Y-%m-%d")
        except ValueError:
            raise ValueError(f"Invalid date format: {date_value}")
    elif isinstance(date_value, datetime.datetime):
        date_obj = date_value
    else:
        raise ValueError("date_value must be a string or datetime object")
    
    operation = operation.lower()
    unit = unit.lower()
    
    # Handle different operations
    if operation == "format":
        # Format date
        return date_obj.strftime(format_str)
    
    elif operation == "add":
        # Add time units
        if unit == "days":
            result = date_obj + datetime.timedelta(days=amount)
        elif unit == "months":
            month = date_obj.month - 1 + amount
            year = date_obj.year + month // 12
            month = month % 12 + 1
            day = min(date_obj.day, [31, 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month - 1])
            result = date_obj.replace(year=year, month=month, day=day)
        elif unit == "years":
            year = date_obj.year + amount
            # Handle leap year for February 29
            if date_obj.month == 2 and date_obj.day == 29:
                day = 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28
            else:
                day = date_obj.day
            result = date_obj.replace(year=year, day=day)
        else:
            raise ValueError(f"Unsupported time unit: {unit}")
        
        return result.strftime(format_str)
    
    elif operation == "subtract":
        # Subtract time units
        return date_functions(date_value, "add", unit, -amount, format_str)
    
    elif operation == "diff":
        # Calculate difference between dates
        if not isinstance(amount, str):
            raise ValueError("For 'diff' operation, amount should be a date string")
            
        try:
            other_date = datetime.datetime.strptime(amount, "%Y-%m-%d")
        except ValueError:
            raise ValueError(f"Invalid date format: {amount}")
        
        if unit == "days":
            return (date_obj - other_date).days
        elif unit == "months":
            return (date_obj.year - other_date.year) * 12 + date_obj.month - other_date.month
        elif unit == "years":
            return date_obj.year - other_date.year
        else:
            raise ValueError(f"Unsupported time unit for diff: {unit}")
    
    elif operation == "extract":
        # Extract part of date
        if unit == "day":
            return date_obj.day
        elif unit == "month":
            return date_obj.month
        elif unit == "year":
            return date_obj.year
        elif unit == "weekday":
            return date_obj.weekday()  # 0=Monday, 6=Sunday
        elif unit == "week":
            return date_obj.isocalendar()[1]  # ISO week number
        else:
            raise ValueError(f"Unsupported extract unit: {unit}")
    
    else:
        raise ValueError(f"Unsupported date operation: {operation}")
    
#########################################################################

########################################################################
#Count Business days      To be test 07/05/2025
########################################################################  

@function_repository.register("temporal", "count_business_days")
def count_business_days(start_date: str, end_date: str, holidays: List[str] = None) -> int:
    """
    Count business days between two dates, excluding weekends and holidays.
    
    Args:
        start_date (str): Start date in ISO format (YYYY-MM-DD)
        end_date (str): End date in ISO format (YYYY-MM-DD)
        holidays (List[str], optional): List of holiday dates to exclude. Defaults to None.
        
    Returns:
        int: Number of business days
    """
    fmt = "%Y-%m-%d"
    start = datetime.datetime.strptime(start_date, fmt).date()
    end = datetime.datetime.strptime(end_date, fmt).date()
    
    # Swap dates if start is after end
    if start > end:
        start, end = end, start
    
    # Parse holidays
    holiday_dates = set()
    if holidays:
        for holiday in holidays:
            try:
                holiday_date = datetime.datetime.strptime(holiday, fmt).date()
                holiday_dates.add(holiday_date)
            except ValueError:
                pass
    
    # Count business days
    count = 0
    current = start
    while current <= end:
        # Check if it's a weekday (0=Monday, 4=Friday, 5-6=Weekend)
        if current.weekday() < 5 and current not in holiday_dates:
            count += 1
        current += datetime.timedelta(days=1)
    
    return count
################################################################################

########################################################################
#Is Business day      To be test 07/05/2025
########################################################################  

@function_repository.register("temporal", "is_business_day")
def is_business_day(date_value: str, holidays: List[str] = None) -> bool:
    """
    Check if a date is a business day (not weekend or holiday).
    
    Args:
        date_value (str): Date in ISO format (YYYY-MM-DD)
        holidays (List[str], optional): List of holiday dates to exclude. Defaults to None.
        
    Returns:
        bool: True if date is a business day, False otherwise
    """
    fmt = "%Y-%m-%d"
    date_obj = datetime.datetime.strptime(date_value, fmt).date()
    
    # Check if it's a weekend
    if date_obj.weekday() >= 5:  # 5=Saturday, 6=Sunday
        return False
    
    # Check if it's a holiday
    if holidays:
        holiday_dates = set()
        for holiday in holidays:
            try:
                holiday_date = datetime.datetime.strptime(holiday, fmt).date()
                holiday_dates.add(holiday_date)
            except ValueError:
                pass
        
        if date_obj in holiday_dates:
            return False
    
    return True

############################################################################

########################################################################
#Conditional business day       To be test 07/05/2025
########################################################################  

@function_repository.register("control_flow", "conditional_logic")
def conditional_logic(
    conditions: Union[str, Dict[str, Any], List[Dict[str, Any]]],
    data: Dict[str, Any] = None,
    default_result: Any = None
) -> Tuple[bool, Any]:
    """
    Evaluate complex conditional expressions with multiple operators.
    
    Args:
        conditions (Union[str, Dict, List]): Condition(s) to evaluate
        data (Dict[str, Any], optional): Data context for condition evaluation. Defaults to None.
        default_result (Any, optional): Result to return if condition is False. Defaults to None.
        
    Returns:
        Tuple[bool, Any]: (result of condition evaluation, result value)
    """
    if data is None:
        data = {}
    
    # String condition format: "field1 = value1 AND field2 > value2"
    if isinstance(conditions, str):
        # Split into parts by AND/OR, preserving parentheses
        condition_str = conditions
        result = _parse_condition_expression(condition_str, data)
        return (result, None if not result else default_result)
    
    # Single condition dictionary: {"field": "age", "operator": ">", "value": 18}
    elif isinstance(conditions, dict) and "field" in conditions and "operator" in conditions and "value" in conditions:
        result = _evaluate_single_condition(conditions, data)
        return (result, conditions.get("result", default_result) if result else default_result)
    
    # Multiple condition objects with logic: {"logic": "and", "conditions": [{...}, {...}]}
    elif isinstance(conditions, dict) and "logic" in conditions and "conditions" in conditions:
        logic = conditions["logic"].lower()
        subconditions = conditions["conditions"]
        
        if logic == "and":
            for condition in subconditions:
                result, _ = conditional_logic(condition, data)
                if not result:
                    return (False, default_result)
            return (True, conditions.get("result", default_result))
        
        elif logic == "or":
            for condition in subconditions:
                result, _ = conditional_logic(condition, data)
                if result:
                    return (True, conditions.get("result", default_result))
            return (False, default_result)
        
        else:
            raise ValueError(f"Unsupported logic operator: {logic}")
    
    # List of conditions (implicit AND)
    elif isinstance(conditions, list):
        for condition in conditions:
            result, _ = conditional_logic(condition, data)
            if not result:
                return (False, default_result)
        return (True, default_result)
    
    else:
        raise ValueError("Unsupported condition format")

def _parse_condition_expression(expr: str, data: Dict[str, Any]) -> bool:
    """Helper function to parse and evaluate a condition expression string."""
    # Simple expression parser for demonstration
    # In a real implementation, this would be more robust
    
    # Handle parentheses first
    paren_match = re.search(r'\(([^()]*)\)', expr)
    if paren_match:
        sub_expr = paren_match.group(1)
        sub_result = _parse_condition_expression(sub_expr, data)
        new_expr = expr.replace(f"({sub_expr})", str(sub_result).lower())
        return _parse_condition_expression(new_expr, data)
    
    # Handle AND operators
    if " AND " in expr:
        parts = expr.split(" AND ")
        return all(_parse_condition_expression(part, data) for part in parts)
    
    # Handle OR operators
    if " OR " in expr:
        parts = expr.split(" OR ")
        return any(_parse_condition_expression(part, data) for part in parts)
    
    # Handle simple condition (field operator value)
    comparison_pattern = r'(\w+)\s*([=<>!]+)\s*\'?([^\']+)\'?'
    match = re.search(comparison_pattern, expr.strip())
    
    if match:
        field = match.group(1)
        operator = match.group(2)
        value = match.group(3).strip("'\"")
        
        # Convert value to appropriate type
        if value.lower() == "true":
            value = True
        elif value.lower() == "false":
            value = False
        elif value.isdigit():
            value = int(value)
        elif re.match(r"^-?\d+\.\d+$", value):
            value = float(value)
        
        # Get field value from data
        field_value = data.get(field)
        
        # Evaluate condition
        return _compare_values(field_value, value, operator)
    
    # Handle boolean literals
    if expr.strip().lower() == "true":
        return True
    elif expr.strip().lower() == "false":
        return False
    
    # Invalid expression
    raise ValueError(f"Invalid condition expression: {expr}")

def _evaluate_single_condition(condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
    """Helper function to evaluate a single condition object."""
    field = condition["field"]
    operator = condition["operator"]
    value = condition["value"]
    
    # Get field value from data
    field_value = data.get(field)
    
    # Evaluate condition
    return _compare_values(field_value, value, operator)

def _compare_values(a: Any, b: Any, operator: str) -> bool:
    """Helper function to compare values with given operator."""
    if operator == "=" or operator == "==":
        return a == b
    elif operator == "!=":
        return a != b
    elif operator == ">":
        return a > b
    elif operator == ">=":
        return a >= b
    elif operator == "<":
        return a < b
    elif operator == "<=":
        return a <= b
    elif operator == "in":
        if isinstance(b, str) and isinstance(a, str):
            return a in b
        elif isinstance(b, (list, tuple)):
            return a in b
        return False
    elif operator == "contains":
        if isinstance(a, str) and isinstance(b, str):
            return b in a
        elif isinstance(a, (list, tuple)):
            return b in a
        return False
    else:
        raise ValueError(f"Unsupported comparison operator: {operator}")
    
#########################################################################    

print("✅ Additional system functions loaded successfully")


print("✅ System functions module loaded successfully with all functions registered")
