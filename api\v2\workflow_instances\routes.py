"""
Workflow Instances Routes for v2 API

This module contains the FastAPI routes for workflow instances endpoints with RBAC checks.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.orm import Session
from typing import Optional

from db.session import get_db
from ..auth.middleware import require_v2_auth, V2SecurityContext
from .models import (
    CreateWorkflowInstanceRequest,
    StartWorkflowInstanceRequest,
    WorkflowInstanceResponse,
    WorkflowInstanceListResponse,
    ErrorResponse,
    SuccessResponse
)
from .service import WorkflowInstancesService


# Create router for workflow instances endpoints
router = APIRouter(
    prefix="/workflow_instances",
    tags=["workflow-instances-v2"],
    responses={
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Forbidden"},
        404: {"model": ErrorResponse, "description": "Not Found"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)


@router.post(
    "/",
    response_model=WorkflowInstanceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new workflow instance",
    description="Create a new workflow instance for a given global objective with RBAC checks",
    responses={
        201: {
            "description": "Workflow instance created successfully",
            "model": WorkflowInstanceResponse
        },
        400: {
            "description": "Invalid request data",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "VALIDATION_ERROR",
                        "message": "Invalid global objective ID",
                        "details": {
                            "go_id": "go001",
                            "tenant_id": "t001"
                        }
                    }
                }
            }
        },
        403: {
            "description": "Access denied",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "ACCESS_DENIED",
                        "message": "User does not have permission to create workflow instances for this global objective",
                        "details": {}
                    }
                }
            }
        }
    }
)
async def create_workflow_instance(
    request: CreateWorkflowInstanceRequest,
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: V2SecurityContext = Depends(require_v2_auth)
) -> WorkflowInstanceResponse:
    """
    Create a new workflow instance for a given global objective.
    
    This endpoint creates a new workflow instance in DRAFT status. The instance
    must be explicitly started using the start endpoint.
    
    Args:
        request: Create workflow instance request data
        tenant_id: Tenant ID from query parameter
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        WorkflowInstanceResponse: Created workflow instance details
        
    Raises:
        HTTPException: 
            - 400 if request data is invalid
            - 403 if user lacks permissions
            - 500 if internal error occurs
    """
    try:
        # Validate tenant_id matches security context
        if security_context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "TENANT_MISMATCH",
                    "message": "Tenant ID does not match authenticated user's tenant",
                    "details": {
                        "provided_tenant_id": tenant_id,
                        "user_tenant_id": security_context.tenant_id
                    }
                }
            )
        
        # Validate request tenant_id matches query parameter
        if request.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "TENANT_ID_MISMATCH",
                    "message": "Tenant ID in request body does not match query parameter",
                    "details": {
                        "request_tenant_id": request.tenant_id,
                        "query_tenant_id": tenant_id
                    }
                }
            )
        
        service = WorkflowInstancesService(db)
        
        result = service.create_workflow_instance(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            request=request
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to create workflow instances for this global objective",
                    "details": {
                        "user_id": security_context.user_id,
                        "tenant_id": tenant_id,
                        "go_id": request.go_id
                    }
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error creating workflow instance: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while creating the workflow instance",
                "details": {}
            }
        )


@router.post(
    "/{instance_id}/start",
    response_model=WorkflowInstanceResponse,
    summary="Start a workflow instance",
    description="Start a workflow instance, transitioning it from DRAFT to ACTIVE status with RBAC checks",
    responses={
        200: {
            "description": "Workflow instance started successfully",
            "model": WorkflowInstanceResponse
        },
        400: {
            "description": "Invalid request or instance not in DRAFT status",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "INVALID_STATUS",
                        "message": "Workflow instance is not in DRAFT status",
                        "details": {
                            "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e",
                            "current_status": "Active"
                        }
                    }
                }
            }
        },
        404: {
            "description": "Workflow instance not found",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "NOT_FOUND",
                        "message": "Workflow instance not found or access denied",
                        "details": {
                            "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e"
                        }
                    }
                }
            }
        }
    }
)
async def start_workflow_instance(
    instance_id: str = Path(..., description="Workflow Instance ID"),
    request: StartWorkflowInstanceRequest = ...,
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: V2SecurityContext = Depends(require_v2_auth)
) -> WorkflowInstanceResponse:
    """
    Start a workflow instance.
    
    This endpoint transitions a workflow instance from DRAFT to ACTIVE status
    and sets the current local objective to the first LO in the workflow.
    
    Args:
        instance_id: Workflow instance ID
        request: Start workflow instance request data
        tenant_id: Tenant ID from query parameter
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        WorkflowInstanceResponse: Started workflow instance details
        
    Raises:
        HTTPException: 
            - 400 if instance is not in DRAFT status
            - 403 if user lacks permissions
            - 404 if instance not found
            - 500 if internal error occurs
    """
    try:
        # Validate tenant_id matches security context
        if security_context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "TENANT_MISMATCH",
                    "message": "Tenant ID does not match authenticated user's tenant",
                    "details": {
                        "provided_tenant_id": tenant_id,
                        "user_tenant_id": security_context.tenant_id
                    }
                }
            )
        
        service = WorkflowInstancesService(db)
        
        result = service.start_workflow_instance(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id,
            request=request
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "Workflow instance not found or access denied",
                    "details": {
                        "instance_id": instance_id,
                        "tenant_id": tenant_id
                    }
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error starting workflow instance: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while starting the workflow instance",
                "details": {}
            }
        )


@router.get(
    "/{instance_id}",
    response_model=WorkflowInstanceResponse,
    summary="Get workflow instance by ID",
    description="Retrieve a specific workflow instance by ID with RBAC checks",
    responses={
        200: {
            "description": "Workflow instance found",
            "model": WorkflowInstanceResponse
        },
        404: {
            "description": "Workflow instance not found",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "NOT_FOUND",
                        "message": "Workflow instance not found or access denied",
                        "details": {
                            "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e"
                        }
                    }
                }
            }
        }
    }
)
async def get_workflow_instance(
    instance_id: str = Path(..., description="Workflow Instance ID"),
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: V2SecurityContext = Depends(require_v2_auth)
) -> WorkflowInstanceResponse:
    """
    Get a specific workflow instance by ID.
    
    Args:
        instance_id: Workflow instance ID
        tenant_id: Tenant ID from query parameter
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        WorkflowInstanceResponse: Workflow instance details
        
    Raises:
        HTTPException: 404 if instance not found or access denied
    """
    try:
        # Validate tenant_id matches security context
        if security_context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "TENANT_MISMATCH",
                    "message": "Tenant ID does not match authenticated user's tenant",
                    "details": {
                        "provided_tenant_id": tenant_id,
                        "user_tenant_id": security_context.tenant_id
                    }
                }
            )
        
        service = WorkflowInstancesService(db)
        
        result = service.get_workflow_instance(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "Workflow instance not found or access denied",
                    "details": {
                        "instance_id": instance_id,
                        "tenant_id": tenant_id
                    }
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving workflow instance {instance_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving the workflow instance",
                "details": {}
            }
        )


@router.get(
    "/",
    response_model=WorkflowInstanceListResponse,
    summary="List workflow instances",
    description="Retrieve workflow instances with optional filtering and pagination, with RBAC checks",
    responses={
        200: {
            "description": "Workflow instances retrieved successfully",
            "model": WorkflowInstanceListResponse
        }
    }
)
async def list_workflow_instances(
    tenant_id: str = Query(..., description="Tenant ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    go_id: Optional[str] = Query(None, description="Filter by global objective ID"),
    page: int = Query(1, description="Page number (1-based)", ge=1),
    page_size: int = Query(10, description="Number of items per page", ge=1, le=100),
    db: Session = Depends(get_db),
    security_context: V2SecurityContext = Depends(require_v2_auth)
) -> WorkflowInstanceListResponse:
    """
    List workflow instances with optional filtering and pagination.
    
    Args:
        tenant_id: Tenant ID from query parameter
        status: Optional status filter
        go_id: Optional global objective ID filter
        page: Page number (1-based)
        page_size: Number of items per page
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        WorkflowInstanceListResponse: List of workflow instances with pagination info
    """
    try:
        # Validate tenant_id matches security context
        if security_context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "TENANT_MISMATCH",
                    "message": "Tenant ID does not match authenticated user's tenant",
                    "details": {
                        "provided_tenant_id": tenant_id,
                        "user_tenant_id": security_context.tenant_id
                    }
                }
            )
        
        service = WorkflowInstancesService(db)
        
        result = service.list_workflow_instances(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            status=status,
            go_id=go_id,
            page=page,
            page_size=page_size
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to list workflow instances",
                    "details": {}
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error listing workflow instances: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while listing workflow instances",
                "details": {}
            }
        )
