#!/usr/bin/env python3
"""
Direct test of the null value fixes in local objectives service.

This script directly tests the service layer to verify that our fixes
for null values are working correctly.
"""

import sys
import os
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging to see our debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from db.session import get_db
    from api.v2.local_objectives.service import LocalObjectiveExecutionService
    from api.v2.auth.middleware import SecurityContext
    from sqlalchemy.sql import text
    
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_test_data(db):
    """Create minimal test data for testing."""
    print("🛠️ Creating test data...")

    try:
        # Use simpler INSERT statements without ON CONFLICT for now
        # Just try to insert and ignore errors if data already exists

        try:
            # Create test tenant
            db.execute(text("""
            INSERT INTO workflow_runtime.tenants (tenant_id, name)
            VALUES ('test_tenant', 'Test Tenant')
            """))
        except:
            pass  # Ignore if already exists

        try:
            # Create test global objective
            db.execute(text("""
            INSERT INTO workflow_runtime.global_objectives (
                go_id, name, description, tenant_id, created_at, updated_at
            ) VALUES (
                'test_go_001', 'Test Global Objective', 'Test GO for null value testing',
                'test_tenant', NOW(), NOW()
            )
            """))
        except:
            pass  # Ignore if already exists

        try:
            # Create test local objective
            db.execute(text("""
            INSERT INTO workflow_runtime.local_objectives (
                lo_id, name, description, go_id, sequence_number, created_at, updated_at
            ) VALUES (
                'test_lo_001', 'Test Local Objective', 'Test LO for null value testing',
                'test_go_001', 1, NOW(), NOW()
            )
            """))
        except:
            pass  # Ignore if already exists

        try:
            # Create test workflow instance
            db.execute(text("""
            INSERT INTO workflow_runtime.workflow_instances (
                instance_id, go_id, tenant_id, status, started_by, started_at,
                current_lo_id, instance_data, is_test, version
            ) VALUES (
                'test_instance_001', 'test_go_001', 'test_tenant', 'In Progress',
                'test_user', NOW(), 'test_lo_001', '{}', TRUE, '1.0'
            )
            """))
        except:
            # Update if already exists
            db.execute(text("""
            UPDATE workflow_runtime.workflow_instances
            SET current_lo_id = 'test_lo_001', status = 'In Progress'
            WHERE instance_id = 'test_instance_001'
            """))

        try:
            # Create test entity and attributes
            db.execute(text("""
            INSERT INTO workflow_runtime.entities (entity_id, name, tenant_id)
            VALUES ('test_entity', 'Test Entity', 'test_tenant')
            """))
        except:
            pass  # Ignore if already exists

        try:
            db.execute(text("""
            INSERT INTO workflow_runtime.entity_attributes (
                attribute_id, entity_id, name, data_type, is_required, default_value
            ) VALUES
                ('attr_001', 'test_entity', 'test_field_1', 'string', FALSE, 'default_value_1'),
                ('attr_002', 'test_entity', 'test_field_2', 'integer', FALSE, '42'),
                ('attr_003', 'test_entity', 'test_field_3', 'string', TRUE, NULL)
            """))
        except:
            pass  # Ignore if already exists

        try:
            # Create test input items
            db.execute(text("""
            INSERT INTO workflow_runtime.lo_input_items (
                slot_id, lo_id, attribute_id, source_type, agent_type, default_value, sequence_number
            ) VALUES
                ('slot_001', 'test_lo_001', 'attr_001', 'user', 'HUMAN', 'default_value_1', 1),
                ('slot_002', 'test_lo_001', 'attr_002', 'user', 'HUMAN', '42', 2),
                ('slot_003', 'test_lo_001', 'attr_003', 'user', 'HUMAN', 'fallback_value', 3)
            """))
        except:
            pass  # Ignore if already exists

        db.commit()
        print("✅ Test data created successfully")
        return True

    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
        return False

def test_null_value_fixes():
    """Test the null value fixes."""
    print("🧪 Testing null value fixes...")
    print("=" * 60)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Create test data
        if not create_test_data(db):
            return False
        
        # Create security context
        security_context = SecurityContext(
            user_id="test_user",
            username="testuser",
            tenant_id="test_tenant",
            roles=["test_role"],
            permissions=[]
        )
        
        # Create service instance
        service = LocalObjectiveExecutionService(db, security_context)
        
        # Test data - simulating user input with some missing values
        test_input_data = {
            "attr_001": "user_provided_value_1",  # User provided value
            # attr_002 is missing - should use default value (42)
            "attr_003": None  # Explicitly null - should use fallback_value
        }
        
        print(f"📤 Test input data: {test_input_data}")
        
        # Execute the local objective
        print("\n🚀 Executing local objective...")
        result = service.execute_local_objective(
            instance_id="test_instance_001",
            tenant_id="test_tenant",
            input_data=test_input_data,
            user_id="test_user"
        )
        
        print(f"📊 Execution result: {result}")
        
        # Verify what was stored in the database
        print("\n🔍 Verifying stored values...")
        verify_query = """
        SELECT lie.input_contextual_id, lie.input_value, ea.name as attribute_name, lii.default_value
        FROM workflow_runtime.lo_input_execution lie
        JOIN workflow_runtime.lo_input_items lii ON lie.input_contextual_id = lii.slot_id
        JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
        WHERE lie.instance_id = 'test_instance_001'
        ORDER BY lii.sequence_number
        """
        
        stored_values = db.execute(text(verify_query)).fetchall()
        
        print("📋 Stored values in database:")
        null_count = 0
        for row in stored_values:
            value_display = row.input_value if row.input_value is not None else "NULL"
            if row.input_value is None:
                null_count += 1
            print(f"   - {row.attribute_name}: {value_display} (default: {row.default_value})")
        
        # Summary
        print(f"\n📊 Summary:")
        print(f"   - Total fields: {len(stored_values)}")
        print(f"   - NULL values: {null_count}")
        print(f"   - Non-NULL values: {len(stored_values) - null_count}")
        
        if null_count == 0:
            print("✅ SUCCESS: No null values found! Fixes are working correctly.")
        else:
            print(f"❌ ISSUE: Found {null_count} null values. Fixes may need adjustment.")
        
        return null_count == 0
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def main():
    """Main test function."""
    print("🔍 Testing Local Objectives - Null Value Fixes")
    print("=" * 60)
    
    success = test_null_value_fixes()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! Null value fixes are working correctly.")
    else:
        print("❌ Tests failed. Check the output above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
