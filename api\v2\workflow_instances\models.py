"""
Workflow Instances Models for v2 API

This module contains the Pydantic models for workflow instances endpoints with RBAC support.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

from models.workflow import WorkflowInstanceStatus


class CreateWorkflowInstanceRequest(BaseModel):
    """Request model for creating a workflow instance."""
    go_id: str = Field(..., description="Global Objective ID")
    tenant_id: str = Field(..., description="Tenant ID")
    user_id: str = Field(..., description="User ID who started the workflow")
    test_mode: bool = Field(False, description="Whether this is a test instance")
    
    class Config:
        schema_extra = {
            "example": {
                "go_id": "go001",
                "tenant_id": "t001",
                "user_id": "user-001",
                "test_mode": False
            }
        }


class StartWorkflowInstanceRequest(BaseModel):
    """Request model for starting a workflow instance."""
    user_id: str = Field(..., description="User ID starting the workflow")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "user-001"
            }
        }


class WorkflowInstanceResponse(BaseModel):
    """Response model for workflow instance operations."""
    instance_id: str = Field(..., description="Workflow instance ID")
    go_id: str = Field(..., description="Global Objective ID")
    tenant_id: str = Field(..., description="Tenant ID")
    status: WorkflowInstanceStatus = Field(..., description="Current status")
    started_by: str = Field(..., description="User who started the workflow")
    started_at: datetime = Field(..., description="When the workflow was started")
    completed_at: Optional[datetime] = Field(None, description="When the workflow was completed")
    current_lo_id: Optional[str] = Field(None, description="Current Local Objective ID")
    instance_data: Dict[str, Any] = Field(default_factory=dict, description="Instance data")
    is_test: bool = Field(False, description="Whether this is a test instance")
    version: str = Field(..., description="Workflow version")
    
    class Config:
        schema_extra = {
            "example": {
                "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e",
                "go_id": "go001",
                "tenant_id": "t001",
                "status": "Draft",
                "started_by": "user-001",
                "started_at": "2025-03-18T04:21:34.078548",
                "completed_at": None,
                "current_lo_id": None,
                "instance_data": {},
                "is_test": False,
                "version": "1.0"
            }
        }


class WorkflowInstanceListResponse(BaseModel):
    """Response model for listing workflow instances."""
    instances: List[WorkflowInstanceResponse] = Field(..., description="List of workflow instances")
    total_count: int = Field(..., description="Total number of instances")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    
    class Config:
        schema_extra = {
            "example": {
                "instances": [
                    {
                        "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e",
                        "go_id": "go001",
                        "tenant_id": "t001",
                        "status": "Active",
                        "started_by": "user-001",
                        "started_at": "2025-03-18T04:21:34.078548",
                        "completed_at": None,
                        "current_lo_id": "4d5f8d06-ea55-52f3-abd0-2ff8cd8c2900",
                        "instance_data": {},
                        "is_test": False,
                        "version": "1.0"
                    }
                ],
                "total_count": 1,
                "page": 1,
                "page_size": 10
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response model."""
    error: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional error details")
    
    class Config:
        schema_extra = {
            "example": {
                "error": "VALIDATION_ERROR",
                "message": "Invalid global objective ID",
                "details": {
                    "go_id": "go001",
                    "tenant_id": "t001"
                }
            }
        }


class SuccessResponse(BaseModel):
    """Standard success response model."""
    success: bool = Field(True, description="Operation success status")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional response data")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Workflow instance created successfully",
                "data": {
                    "instance_id": "c25bfb7d-ba2d-419c-a971-538ecafebe7e"
                }
            }
        }
