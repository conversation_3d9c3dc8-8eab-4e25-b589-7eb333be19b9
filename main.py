from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging

from core.config import settings
from api.v2.api import api_router as api_v2_router


# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import services
from services.function_repository import function_repository

# Explicitly import system_functions to register all functions
from services import system_functions
from services.system_functions import init_system_functions

# Important: Call the initialization function to verify registration
if not init_system_functions():
    logger.error("❌ ERROR: No functions registered in Function Repository!")
    raise RuntimeError("No functions registered in Function Repository!")
else:
    logger.info("✅ Function repository successfully initialized with functions")


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.VERSION,
    docs_url="/docs",  # ✅ Enables Swagger UI
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# Set up CORS with more specific configuration
app.add_middleware(
    CORSMiddleware,
    # Include both wildcard and specific origins
    allow_origins=["*", "http://localhost:61623", "http://**********:8010"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
    expose_headers=["Content-Length", "Content-Type"],
    max_age=600,  # Cache preflight requests for 10 minutes
)

# Include API routers (V2 only)
app.include_router(api_v2_router, prefix="/api")

@app.get("/")
def root():
    return {"message": "Welcome to the Workflow Engine API"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}

# Error handler for custom exceptions
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"An unexpected error occurred: {str(exc)}"}
    )
