"""
Local Objectives Routes for v2 API

This module contains the FastAPI routes for local objectives operations with RBAC checks.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any

from db.session import get_db
from ..auth.middleware import get_security_context, require_auth, SecurityContext
from .models import LocalObjectiveInputsResponse, ErrorResponse, DropdownOption, ExecuteLocalObjectiveRequest, ExecuteLocalObjectiveResponse
from .service import LocalObjectivesService, LocalObjectiveExecutionService
from .dependent_dropdown import DependentDropdownService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/local_objectives",
    tags=["local-objectives-v2"],
    responses={
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Forbidden"},
        404: {"model": ErrorResponse, "description": "Not Found"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)

@router.get(
    "/instances/{instance_id}/inputs",
    response_model=LocalObjectiveInputsResponse,
    summary="Get workflow instance inputs",
    description="""
    Retrieve all input fields for a workflow instance's current local objective.
    
    This endpoint provides comprehensive input field information including:
    - User input fields (require user interaction)
    - System input fields (automatically calculated)
    - Information fields (display-only)
    - Dependent fields (values depend on other fields)
    - Dropdown options for fields with data sources
    - Current values for existing workflow instances
    
    The response categorizes inputs by source type and includes dependency mappings
    for handling dependent dropdowns and conditional fields.
    
    **Key Features:**
    - RBAC-based access control
    - Multi-tenant data isolation
    - System function execution for calculated fields
    - Dropdown data source resolution
    - Dependency mapping for conditional fields
    - Multi-select support
    
    **Use Cases:**
    - Building dynamic forms for workflow execution
    - Displaying current state of workflow instances
    - Handling dependent dropdown scenarios (e.g., leave type -> leave subtype)
    - Supporting multi-select inputs (e.g., multiple symptoms, multiple leave types)
    """
)
async def get_workflow_instance_inputs(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    tenant_id: str = Query(..., description="Tenant ID to filter data"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> LocalObjectiveInputsResponse:
    """
    Get all input fields for a workflow instance's current local objective.
    
    This is the main fetch inputs API that handles:
    1. RBAC permission checks
    2. Workflow instance validation
    3. Input field categorization by source type
    4. System function execution for calculated fields
    5. Dropdown data source resolution
    6. Dependency mapping for conditional fields
    """
    try:
        logger.info(f"Fetching inputs for workflow instance {instance_id} by user {security_context.user_id}")
        
        service = LocalObjectivesService(db)
        
        result = service.get_workflow_instance_inputs(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            logger.warning(f"Access denied or instance not found: {instance_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access this workflow instance or instance not found",
                    "details": {
                        "instance_id": instance_id,
                        "tenant_id": tenant_id
                    }
                }
            )
        
        logger.info(f"Successfully retrieved {len(result.user_inputs + result.system_inputs + result.info_inputs + result.dependent_inputs)} input fields for instance {instance_id}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting workflow instance inputs: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving workflow instance inputs",
                "details": {
                    "instance_id": instance_id,
                    "error_details": str(e)
                }
            }
        )

@router.get(
    "/instances/{instance_id}/inputs/{field_id}/dependent-options",
    response_model=List[DropdownOption],
    summary="Get dependent dropdown options",
    description="""
    Get dropdown options for a dependent field based on parent field value.
    
    This endpoint is used for dependent dropdowns where the options for a child field
    depend on the selected value of a parent field.
    
    **Examples:**
    - Leave subtype options based on selected leave type
    - City options based on selected state/province
    - Product models based on selected brand
    
    **Usage:**
    ```
    GET /api/v2/local_objectives/instances/{instance_id}/inputs/{field_id}/dependent-options?parent_value=sick_leave&tenant_id=t001
    ```
    
    **Response:**
    ```json
    [
      {"value": "medical_leave", "label": "Medical Leave", "metadata": {}},
      {"value": "emergency_leave", "label": "Emergency Leave", "metadata": {}}
    ]
    ```
    """
)
async def get_dependent_dropdown_options(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    field_id: str = Path(..., description="Child field ID (e.g., 'GO1.LO1.IP1.IT8')"),
    parent_value: str = Query(..., description="Selected value of the parent field"),
    tenant_id: str = Query(..., description="Tenant ID to filter data"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> List[DropdownOption]:
    """
    Get dropdown options for a dependent field based on parent field value.
    
    This endpoint handles dependent dropdown scenarios where child field options
    are filtered based on the selected value of a parent field.
    """
    try:
        logger.info(f"Getting dependent options for field {field_id} with parent value: {parent_value}")
        
        # First verify user has access to this workflow instance
        service = LocalObjectivesService(db)
        
        # Check access by trying to get the workflow instance inputs
        result = service.get_workflow_instance_inputs(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            logger.warning(f"Access denied or instance not found: {instance_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access this workflow instance or instance not found",
                    "details": {
                        "instance_id": instance_id,
                        "field_id": field_id
                    }
                }
            )
        
        # Get dependent dropdown options
        dropdown_service = DependentDropdownService(db)
        
        options = dropdown_service.get_dependent_options(
            child_field_id=field_id,
            parent_field_value=parent_value,
            additional_filters={"tenant_id": tenant_id}
        )
        
        logger.info(f"Successfully retrieved {len(options)} dependent options for field {field_id}")
        
        return options
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting dependent dropdown options: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving dependent dropdown options",
                "details": {
                    "instance_id": instance_id,
                    "field_id": field_id,
                    "parent_value": parent_value,
                    "error_details": str(e)
                }
            }
        )

@router.get(
    "/instances/{instance_id}/inputs/{field_id}/system-dependent-options",
    response_model=List[DropdownOption],
    summary="Get system dependent dropdown options",
    description="""
    Get dropdown options for system_dependent source type fields.
    
    This endpoint is used for fields with source_type='system_dependent' where
    the options are generated by executing a nested function with parent field values.
    
    **Usage:**
    ```
    GET /api/v2/local_objectives/instances/{instance_id}/inputs/{field_id}/system-dependent-options?parent_values={"leave_type":"sick","employee_id":"123"}&tenant_id=t001
    ```
    """
)
async def get_system_dependent_options(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    field_id: str = Path(..., description="Field ID with system_dependent source type"),
    parent_values: str = Query(..., description="JSON string of parent field values"),
    tenant_id: str = Query(..., description="Tenant ID to filter data"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> List[DropdownOption]:
    """
    Get dropdown options for system_dependent source type fields.
    
    This endpoint executes nested functions to generate dropdown options
    based on multiple parent field values.
    """
    try:
        import json
        
        logger.info(f"Getting system dependent options for field {field_id} with parent values: {parent_values}")
        
        # Parse parent values JSON
        try:
            parent_values_dict = json.loads(parent_values)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "INVALID_JSON",
                    "message": "parent_values must be valid JSON",
                    "details": {"parent_values": parent_values}
                }
            )
        
        # First verify user has access to this workflow instance
        service = LocalObjectivesService(db)
        
        result = service.get_workflow_instance_inputs(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access this workflow instance or instance not found",
                    "details": {}
                }
            )
        
        # Find the field and get its nested function ID
        nested_function_id = None
        for field in result.dependent_inputs:
            if field.item_id == field_id and field.source_type == "system_dependent":
                nested_function_id = field.nested_function_id
                break
        
        if not nested_function_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "FIELD_NOT_FOUND",
                    "message": f"System dependent field {field_id} not found or not system_dependent type",
                    "details": {"field_id": field_id}
                }
            )
        
        # Get system dependent options
        dropdown_service = DependentDropdownService(db)
        
        options = dropdown_service.get_system_dependent_options(
            child_field_id=field_id,
            parent_values=parent_values_dict,
            nested_function_id=nested_function_id
        )
        
        logger.info(f"Successfully retrieved {len(options)} system dependent options for field {field_id}")
        
        return options
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting system dependent options: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving system dependent options",
                "details": {
                    "instance_id": instance_id,
                    "field_id": field_id,
                    "error_details": str(e)
                }
            }
        )

@router.get(
    "/instances/{instance_id}/inputs/user",
    response_model=LocalObjectiveInputsResponse,
    summary="Get user input fields only",
    description="""
    Retrieve only user input fields for a workflow instance.
    
    This endpoint returns a filtered view containing only fields that require user interaction,
    excluding system-calculated and information-only fields. Useful for building minimal
    input forms or when you only need interactive fields.
    """
)
async def get_user_inputs_only(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    tenant_id: str = Query(..., description="Tenant ID to filter data"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> LocalObjectiveInputsResponse:
    """Get only user input fields for a workflow instance."""
    try:
        logger.info(f"Fetching user inputs only for workflow instance {instance_id}")
        
        service = LocalObjectivesService(db)
        
        result = service.get_workflow_instance_inputs(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access this workflow instance or instance not found",
                    "details": {}
                }
            )
        
        # Filter to return only user inputs
        filtered_result = LocalObjectiveInputsResponse(
            local_objective=result.local_objective,
            user_inputs=result.user_inputs,
            system_inputs=[],  # Empty for user-only view
            info_inputs=[],    # Empty for user-only view
            dependent_inputs=result.dependent_inputs,  # Keep dependent inputs as they may be user-interactive
            dependencies=result.dependencies
        )
        
        return filtered_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting user inputs: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving user inputs",
                "details": {}
            }
        )

@router.get(
    "/instances/{instance_id}/inputs/system",
    response_model=LocalObjectiveInputsResponse,
    summary="Get system input fields only",
    description="""
    Retrieve only system-calculated input fields for a workflow instance.
    
    This endpoint returns fields that are automatically calculated by the system,
    including results from nested function executions. Useful for debugging
    system calculations or displaying computed values.
    """
)
async def get_system_inputs_only(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    tenant_id: str = Query(..., description="Tenant ID to filter data"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> LocalObjectiveInputsResponse:
    """Get only system input fields for a workflow instance."""
    try:
        logger.info(f"Fetching system inputs only for workflow instance {instance_id}")
        
        service = LocalObjectivesService(db)
        
        result = service.get_workflow_instance_inputs(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access this workflow instance or instance not found",
                    "details": {}
                }
            )
        
        # Filter to return only system inputs
        filtered_result = LocalObjectiveInputsResponse(
            local_objective=result.local_objective,
            user_inputs=[],    # Empty for system-only view
            system_inputs=result.system_inputs,
            info_inputs=[],    # Empty for system-only view
            dependent_inputs=[], # Empty for system-only view
            dependencies={}    # Empty for system-only view
        )
        
        return filtered_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting system inputs: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving system inputs",
                "details": {}
            }
        )

@router.get(
    "/instances/{instance_id}/inputs/dependencies",
    response_model=dict,
    summary="Get input field dependencies",
    description="""
    Retrieve the dependency mapping for input fields in a workflow instance.
    
    This endpoint returns a mapping of parent fields to their dependent child fields,
    useful for implementing dependent dropdown functionality and conditional field display.
    
    Example response:
    ```json
    {
      "leave_type": ["leave_subtype", "documentation_required"],
      "employee_department": ["manager_list", "approval_workflow"]
    }
    ```
    """
)
async def get_input_dependencies(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    tenant_id: str = Query(..., description="Tenant ID to filter data"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> dict:
    """Get dependency mapping for input fields."""
    try:
        logger.info(f"Fetching input dependencies for workflow instance {instance_id}")
        
        service = LocalObjectivesService(db)
        
        result = service.get_workflow_instance_inputs(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access this workflow instance or instance not found",
                    "details": {}
                }
            )
        
        return result.dependencies
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting input dependencies: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving input dependencies",
                "details": {}
            }
        )

@router.post(
    "/instances/{instance_id}/execute",
    response_model=ExecuteLocalObjectiveResponse,
    summary="Execute local objective",
    description="""
    Execute the current local objective in a workflow instance with v2 microservices architecture.
    
    This endpoint handles the complete execution flow following your specification:
    1. Get workflow instance ID from user
    2. Check user role and access permissions for the LO
    3. Get input fields and categorize by type:
       - User inputs: from API request
       - System function inputs: execute nested functions
       - Constant inputs: fetch from database
       - Information inputs: ignore for execution
       - Mapping inputs: already in lo_input_execution from previous LOs
    4. Execute primary function of the local objective
    5. Store outputs in lo_output_execution
    6. Process data mappings to next LOs
    7. Determine and set next LO
    8. Create individual LO execution table record for tracking
    9. Update workflow transaction log
    
    **Key Features:**
    - Individual LO tables for easy tracking (GO1_LO1_SubmitLeaveRequest format)
    - Complete RBAC permission checking
    - Type-based input processing as per your specification
    - Comprehensive audit trail
    - Microservices architecture compliance
    
    **Request Body:**
    ```json
    {
      "input_data": {
        "leave_type": "annual",
        "start_date": "2024-01-15",
        "end_date": "2024-01-20"
      },
      "user_id": "user123"
    }
    ```
    
    **Response:**
    ```json
    {
      "status": "Completed",
      "message": "Local Objective LO001 executed successfully",
      "output": {
        "leave_id": "LEAVE_ABC123",
        "status": "submitted"
      },
      "next_lo_id": "LO002",
      "execution_id": "instance_123_LO001",
      "lo_execution_table": "GO1_LO1_SubmitLeaveRequest"
    }
    ```
    """
)
async def execute_local_objective(
    instance_id: str = Path(..., description="Workflow instance ID (UUID format)"),
    request: ExecuteLocalObjectiveRequest = ...,
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> ExecuteLocalObjectiveResponse:
    """
    Execute local objective with v2 microservices architecture.
    
    This is the main execute API that replaces the v1 implementation with:
    - Enhanced RBAC checks
    - Type-based input processing
    - Individual LO table creation
    - Comprehensive audit trail
    - Microservices architecture compliance
    """
    try:
        logger.info(f"Executing LO for instance {instance_id} by user {security_context.user_id}")
        
        # Create execution service
        execution_service = LocalObjectiveExecutionService(db)
        
        # Execute the local objective
        result = execution_service.execute_local_objective(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            instance_id=instance_id,
            input_data=request.input_data
        )
        
        if result is None:
            logger.warning(f"User {security_context.user_id} denied execution access to instance {instance_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "EXECUTION_DENIED",
                    "message": "User does not have permission to execute this workflow instance",
                    "details": {
                        "instance_id": instance_id,
                        "tenant_id": tenant_id,
                        "user_id": security_context.user_id
                    }
                }
            )
        
        # Convert result to response model
        response = ExecuteLocalObjectiveResponse(
            status=result["status"],
            message=result["message"],
            output=result.get("output", {}),
            next_lo_id=result.get("next_lo_id"),
            execution_id=result.get("execution_id"),
            lo_execution_table=result.get("lo_execution_table")
        )
        
        logger.info(f"Successfully executed LO for instance {instance_id}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error executing local objective: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "EXECUTION_ERROR",
                "message": "An unexpected error occurred during local objective execution",
                "details": {
                    "instance_id": instance_id,
                    "error_details": str(e)
                }
            }
        )

# Health check endpoint for the local objectives microservice
@router.get(
    "/health",
    summary="Health check",
    description="Check the health status of the local objectives microservice"
)
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "service": "local_objectives_v2",
        "version": "2.0.0"
    }
