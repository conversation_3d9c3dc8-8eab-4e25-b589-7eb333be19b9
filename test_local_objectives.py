#!/usr/bin/env python3
"""
Test script for Local Objectives API to verify null value fixes.

This script tests the local objectives execution endpoint to ensure that
input values are properly stored in the database instead of showing as null.
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v2"

def test_health():
    """Test if the server is running."""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False

def get_auth_token():
    """Get authentication token for testing."""
    # For now, we'll use a test token
    # In a real scenario, you'd authenticate first
    return "test-token"

def test_local_objectives_inputs(instance_id, token):
    """Test getting inputs for a local objective."""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        url = f"{API_BASE}/local_objectives/instances/{instance_id}/inputs"
        print(f"🔍 Testing GET {url}")
        
        response = requests.get(url, headers=headers)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Successfully retrieved inputs")
            print(f"📋 Input fields found: {len(data.get('inputs', []))}")
            
            # Show input structure
            for input_field in data.get('inputs', [])[:3]:  # Show first 3
                print(f"   - {input_field.get('attribute_name', 'Unknown')}: {input_field.get('source_type', 'Unknown')}")
            
            return data
        else:
            print(f"❌ Failed to get inputs: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing inputs: {e}")
        return None

def test_local_objectives_execution(instance_id, token, input_data):
    """Test executing a local objective with input data."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        url = f"{API_BASE}/local_objectives/instances/{instance_id}/execute"
        print(f"🚀 Testing POST {url}")
        print(f"📤 Input data: {json.dumps(input_data, indent=2)}")
        
        payload = {
            "input_data": input_data,
            "tenant_id": "test_tenant"
        }
        
        response = requests.post(url, headers=headers, json=payload)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Successfully executed local objective")
            print(f"📋 Response: {json.dumps(data, indent=2)}")
            return data
        else:
            print(f"❌ Failed to execute: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing execution: {e}")
        return None

def main():
    """Main test function."""
    print("🧪 Testing Local Objectives API - Null Value Fixes")
    print("=" * 60)
    
    # Test server health
    if not test_health():
        sys.exit(1)
    
    # Get auth token
    token = get_auth_token()
    print(f"🔑 Using token: {token[:20]}...")
    
    # Test with a sample instance ID
    # You'll need to replace this with an actual instance ID from your database
    test_instance_id = "test-instance-001"
    
    print(f"\n📋 Testing with instance ID: {test_instance_id}")
    
    # Test getting inputs first
    print("\n1️⃣ Testing GET inputs...")
    inputs_data = test_local_objectives_inputs(test_instance_id, token)
    
    # Test execution with sample data
    print("\n2️⃣ Testing POST execution...")
    sample_input_data = {
        "field1": "test_value_1",
        "field2": "test_value_2", 
        "field3": 123,
        "field4": True
    }
    
    execution_result = test_local_objectives_execution(test_instance_id, token, sample_input_data)
    
    print("\n" + "=" * 60)
    if execution_result:
        print("✅ Test completed successfully!")
        print("🔍 Check the server logs for debugging output showing:")
        print("   - API DEBUG: RECEIVED INPUT DATA")
        print("   - Service layer processing")
        print("   - Database storage verification")
    else:
        print("❌ Test failed - check server logs for errors")

if __name__ == "__main__":
    main()
