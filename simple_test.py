#!/usr/bin/env python3
"""
Simple test to check existing data and test null value fixes.
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging to see our debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from db.session import get_db
    from sqlalchemy.sql import text
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def check_existing_data():
    """Check what data exists in the database."""
    print("🔍 Checking existing data...")
    
    db = next(get_db())
    
    try:
        # Check workflow instances
        print("\n📋 Workflow Instances:")
        instances = db.execute(text("""
        SELECT instance_id, go_id, status, current_lo_id 
        FROM workflow_runtime.workflow_instances 
        LIMIT 5
        """)).fetchall()
        
        if instances:
            for row in instances:
                print(f"   - {row.instance_id}: GO={row.go_id}, Status={row.status}, LO={row.current_lo_id}")
        else:
            print("   No workflow instances found")
        
        # Check global objectives
        print("\n🎯 Global Objectives:")
        gos = db.execute(text("""
        SELECT go_id, name 
        FROM workflow_runtime.global_objectives 
        LIMIT 5
        """)).fetchall()
        
        if gos:
            for row in gos:
                print(f"   - {row.go_id}: {row.name}")
        else:
            print("   No global objectives found")
        
        # Check local objectives
        print("\n🎯 Local Objectives:")
        los = db.execute(text("""
        SELECT lo_id, name, go_id 
        FROM workflow_runtime.local_objectives 
        LIMIT 5
        """)).fetchall()
        
        if los:
            for row in los:
                print(f"   - {row.lo_id}: {row.name} (GO: {row.go_id})")
        else:
            print("   No local objectives found")
        
        # Check input items
        print("\n📝 Input Items:")
        inputs = db.execute(text("""
        SELECT slot_id, lo_id, attribute_id, source_type, agent_type, default_value
        FROM workflow_runtime.lo_input_items 
        LIMIT 5
        """)).fetchall()
        
        if inputs:
            for row in inputs:
                print(f"   - {row.slot_id}: LO={row.lo_id}, Attr={row.attribute_id}, Type={row.source_type}, Default={row.default_value}")
        else:
            print("   No input items found")
        
        # Check existing input execution data
        print("\n💾 Input Execution Data:")
        executions = db.execute(text("""
        SELECT instance_id, lo_id, input_contextual_id, input_value
        FROM workflow_runtime.lo_input_execution 
        ORDER BY created_at DESC
        LIMIT 10
        """)).fetchall()
        
        if executions:
            null_count = 0
            for row in executions:
                value_display = row.input_value if row.input_value is not None else "NULL"
                if row.input_value is None:
                    null_count += 1
                print(f"   - Instance: {row.instance_id}, LO: {row.lo_id}, Value: {value_display}")
            
            print(f"\n📊 Current null value count: {null_count} out of {len(executions)} records")
            if null_count > 0:
                print("❌ Found null values in database - this is the issue we're fixing!")
            else:
                print("✅ No null values found in recent executions")
        else:
            print("   No input execution data found")
        
        return instances, los, inputs
        
    except Exception as e:
        print(f"❌ Error checking data: {e}")
        return [], [], []
    finally:
        db.close()

def test_service_directly():
    """Test the service layer directly with mock data."""
    print("\n🧪 Testing service layer directly...")
    
    try:
        from api.v2.local_objectives.service import LocalObjectiveExecutionService
        from api.v2.auth.middleware import SecurityContext
        
        # Create mock security context
        security_context = SecurityContext(
            user_id="test_user",
            username="testuser", 
            tenant_id="test_tenant",
            roles=["test_role"],
            permissions=[]
        )
        
        db = next(get_db())
        service = LocalObjectiveExecutionService(db, security_context)
        
        # Test the input processing logic with sample data
        print("🔍 Testing input processing logic...")
        
        # Mock input data
        test_input_data = {
            "field1": "user_value_1",
            "field2": None,  # This should get default value
            # field3 is missing - should also get default value
        }
        
        print(f"📤 Test input: {test_input_data}")
        
        # Test the _process_inputs_by_type method if it exists
        if hasattr(service, '_process_inputs_by_type'):
            print("✅ Found _process_inputs_by_type method")
        else:
            print("❌ _process_inputs_by_type method not found")
        
        # Test the main execute method with a mock instance
        print("🚀 Testing execute method...")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing service: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("🔍 Simple Database and Service Test")
    print("=" * 50)
    
    # Check existing data
    instances, los, inputs = check_existing_data()
    
    # Test service layer
    test_service_directly()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print(f"   - Workflow instances: {len(instances)}")
    print(f"   - Local objectives: {len(los)}")
    print(f"   - Input items: {len(inputs)}")
    
    if instances and los and inputs:
        print("✅ Database has test data available")
        print("🎯 You can test with these instance IDs:")
        for instance in instances[:3]:
            print(f"   - {instance.instance_id}")
    else:
        print("❌ Database needs test data to be created")
    
    print("\n💡 Next steps:")
    print("   1. Use Swagger UI at http://localhost:8000/docs")
    print("   2. Test the /api/v2/local_objectives endpoints")
    print("   3. Check server logs for our debugging output")

if __name__ == "__main__":
    main()
