"""
Local Objectives Service for v2 API

This module contains the business logic for local objectives operations with RBAC checks.
"""

import logging
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from .models import (
    InputFieldResponse, 
    LocalObjectiveInputsResponse, 
    DropdownDataSource,
    NestedFunctionDefinition,
    NestedFunctionExecutionResult,
    InputExecutionRecord
)
from .business_rules_validator import BusinessRulesValidator

logger = logging.getLogger(__name__)

class RBACService:
    """Service class for Role-Based Access Control operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def check_user_permissions(self, user_id: str, tenant_id: str, resource: str, action: str) -> bool:
        """Check if user has permission to perform action on resource."""
        try:
            self.logger.info(f"Checking permissions for user {user_id}, tenant {tenant_id}, resource {resource}, action {action}")
            
            # Get user role IDs (not role names)
            role_query = """
            SELECT DISTINCT r.role_id
            FROM workflow_runtime.user_roles ur
            JOIN workflow_runtime.roles r ON ur.role = r.role_id
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            role_result = self.db.execute(text(role_query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchall()
            
            if not role_result:
                self.logger.warning(f"No roles found for user {user_id} in tenant {tenant_id}")
                return False
            
            user_role_ids = [row[0] for row in role_result]
            self.logger.info(f"User {user_id} has role IDs: {user_role_ids}")
            
            # Check LO-specific permissions from role_system_permissions table
            permission_query = """
            SELECT rsp.granted_actions
            FROM workflow_runtime.role_system_permissions rsp
            JOIN workflow_runtime.system_permissions sp ON rsp.permission_id = sp.permission_id
            WHERE rsp.role_id = ANY(:role_ids) 
            AND sp.resource_identifier = :resource
            """
            
            permission_result = self.db.execute(text(permission_query), {
                "role_ids": user_role_ids,
                "resource": resource
            }).fetchall()
            
            # Check if any role has the required action
            for row in permission_result:
                granted_actions = row[0]  # This is a JSON array
                if isinstance(granted_actions, list):
                    # Map action types
                    if action == "write" and any(a in granted_actions for a in ["create", "update", "write"]):
                        self.logger.info(f"User {user_id} granted {action} access to {resource}")
                        return True
                    elif action == "read" and "read" in granted_actions:
                        self.logger.info(f"User {user_id} granted {action} access to {resource}")
                        return True
                    elif action == "delete" and "delete" in granted_actions:
                        self.logger.info(f"User {user_id} granted {action} access to {resource}")
                        return True
            
            # Fallback to basic role permissions for non-LO resources
            if not resource.startswith("GO"):
                user_roles_query = """
                SELECT DISTINCT ur.role
                FROM workflow_runtime.user_roles ur
                WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
                """
                user_roles_result = self.db.execute(text(user_roles_query), {
                    "user_id": user_id, 
                    "tenant_id": tenant_id
                }).fetchall()
                
                user_roles = [row[0] for row in user_roles_result]
                
                if "Administrator" in user_roles:
                    return True
                if "Manager" in user_roles and action in ["read", "write"]:
                    return True
                if "User" in user_roles and action == "read":
                    return True
                if "Viewer" in user_roles and action == "read":
                    return True
            
            self.logger.warning(f"User {user_id} denied {action} access to {resource}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking user permissions: {str(e)}")
            return False
    
    def check_tenant_access(self, user_id: str, tenant_id: str) -> bool:
        """Check if user has access to the specified tenant."""
        try:
            query = """
            SELECT COUNT(*)
            FROM workflow_runtime.user_roles ur
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            result = self.db.execute(text(query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            has_access = result[0] > 0 if result else False
            self.logger.info(f"User {user_id} tenant access to {tenant_id}: {has_access}")
            return has_access
            
        except Exception as e:
            self.logger.error(f"Error checking tenant access: {str(e)}")
            return False

class NestedFunctionService:
    """Service for executing nested functions"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def execute_nested_function(
        self, 
        function_name: str, 
        parameters: Dict[str, Any],
        context_data: Optional[Dict[str, Any]] = None
    ) -> NestedFunctionExecutionResult:
        """Execute a nested function and return the result using function repository."""
        try:
            self.logger.info(f"🔍 DEBUG: NestedFunctionService executing: {function_name} with parameters: {parameters}")
            
            # Import the function repository
            from services.function_repository import function_repository
            
            # Get function definition to understand output variable
            query = """
            SELECT nf.function_name, nf.function_type, nf.parameters, nf.output_to
            FROM workflow_runtime.lo_nested_functions nf
            WHERE nf.function_name = :function_name
            LIMIT 1
            """
            
            db_result = self.db.execute(text(query), {"function_name": function_name}).fetchone()
            
            if not db_result:
                return NestedFunctionExecutionResult(
                    function_name=function_name,
                    result=None,
                    execution_status="error",
                    error_message=f"Function {function_name} not found in database"
                )
            
            # Merge context_data into parameters for function execution
            execution_params = parameters.copy()
            if context_data:
                execution_params.update(context_data)
            
            self.logger.info(f"🔍 DEBUG: Calling function_repository.auto_execute for {function_name}")
            self.logger.info(f"🔍 DEBUG: Execution parameters: {execution_params}")
            
            # Execute the function using function repository
            result_value = function_repository.auto_execute(
                function_name, 
                self.db, 
                **execution_params
            )
            
            self.logger.info(f"🔍 DEBUG: Function {function_name} returned: {result_value}")
            
            return NestedFunctionExecutionResult(
                function_name=function_name,
                result=result_value,
                output_variable=db_result.output_to,
                execution_status="success",
                error_message=None
            )
            
        except Exception as e:
            self.logger.error(f"🔍 DEBUG: Error executing nested function {function_name}: {str(e)}")
            return NestedFunctionExecutionResult(
                function_name=function_name,
                result=None,
                execution_status="error",
                error_message=str(e)
            )

class DropdownService:
    """Service for handling dropdown data sources"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def get_dropdown_options(
        self, 
        input_item_id: str, 
        parent_values: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get dropdown options for an input field."""
        try:
            self.logger.info(f"Getting dropdown options for input_item_id: {input_item_id}")
            
            # Get dropdown data source configuration
            query = """
            SELECT dds.source_type, dds.query_text, dds.function_name, dds.function_params,
                   dds.value_field, dds.display_field, dds.depends_on_fields
            FROM workflow_runtime.dropdown_data_sources dds
            WHERE dds.input_item_id = :input_item_id
            """
            
            result = self.db.execute(text(query), {"input_item_id": input_item_id}).fetchone()
            
            if not result:
                self.logger.warning(f"No dropdown data source found for input_item_id: {input_item_id}")
                return []
            
            source_type = result.source_type
            
            if source_type == "database":
                return self._execute_database_query(result, parent_values)
            elif source_type == "function":
                return self._execute_function_query(result, parent_values)
            else:
                self.logger.warning(f"Unknown dropdown source type: {source_type}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting dropdown options: {str(e)}")
            return []
    
    def _execute_database_query(self, source_config, parent_values: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute database query for dropdown options."""
        try:
            query_text = source_config.query_text
            value_field = source_config.value_field
            display_field = source_config.display_field
            
            # Replace parameters in query if parent values provided
            params = {}
            if parent_values:
                for key, value in parent_values.items():
                    params[key] = value
                    query_text = query_text.replace(f":{key}", f":param_{key}")
                    params[f"param_{key}"] = value
            
            self.logger.info(f"Executing dropdown query: {query_text} with params: {params}")
            
            result = self.db.execute(text(query_text), params).fetchall()
            
            options = []
            for row in result:
                # Convert row to dict if needed
                if hasattr(row, '_asdict'):
                    row_dict = row._asdict()
                else:
                    row_dict = dict(row)
                
                options.append({
                    "value": row_dict.get(value_field),
                    "label": row_dict.get(display_field)
                })
            
            return options
            
        except Exception as e:
            self.logger.error(f"Error executing database query for dropdown: {str(e)}")
            return []
    
    def _execute_function_query(self, source_config, parent_values: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute function for dropdown options."""
        try:
            function_name = source_config.function_name
            function_params = source_config.function_params or {}
            
            # Merge parent values into function parameters
            if parent_values:
                function_params.update(parent_values)
            
            # Execute the function (mock implementation)
            nested_service = NestedFunctionService(self.db)
            result = nested_service.execute_nested_function(function_name, function_params)
            
            if result.execution_status == "success" and isinstance(result.result, list):
                return result.result
            else:
                self.logger.error(f"Function {function_name} failed or returned invalid result")
                return []
                
        except Exception as e:
            self.logger.error(f"Error executing function for dropdown: {str(e)}")
            return []

class LocalObjectiveExecutionService:
    """Service for executing local objectives with v2 microservices approach"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self.rbac = RBACService(db_session)
        self.nested_function_service = NestedFunctionService(db_session)
    
    def execute_local_objective(
        self, 
        user_id: str, 
        tenant_id: str,
        instance_id: str,
        input_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Execute local objective with v2 microservices approach with proper transaction management"""
        
        try:
            self.logger.info(f"🚀 STARTING LO EXECUTION for instance {instance_id} by user {user_id}")
            
            # Step 1: RBAC Check
            self.logger.info(f"📋 STEP 1: Starting RBAC permission check...")
            if not self._check_execution_permissions(user_id, tenant_id, instance_id):
                self.logger.warning(f"❌ STEP 1 FAILED: User {user_id} denied execution access to instance {instance_id}")
                return None
            self.logger.info(f"✅ STEP 1 COMPLETED: RBAC permission check passed")
                
            # Step 2: Get workflow instance and validate
            self.logger.info(f"📋 STEP 2: Getting and validating workflow instance...")
            instance_info = self._get_workflow_instance(instance_id, tenant_id)
            if not instance_info:
                self.logger.warning(f"❌ STEP 2 FAILED: Workflow instance {instance_id} not found or invalid")
                return None
            self.logger.info(f"✅ STEP 2 COMPLETED: Instance info retrieved - LO: {instance_info['current_lo_id']}, GO: {instance_info['go_id']}")
                
            # Step 3: Process inputs by type
            self.logger.info(f"📋 STEP 3: Processing all input types for LO {instance_info['current_lo_id']}...")
            processed_inputs = self._process_all_input_types(
                instance_id, instance_info['current_lo_id'], input_data, user_id
            )
            self.logger.info(f"✅ STEP 3 COMPLETED: Processed {len(processed_inputs)} inputs: {list(processed_inputs.keys())}")
            
            # Step 4: Execute primary function with proper error checking
            self.logger.info(f"📋 STEP 4: Executing primary function for LO {instance_info['current_lo_id']}...")
            execution_result = self._execute_primary_function(
                instance_info['current_lo_id'], processed_inputs
            )
            
            # CRITICAL: Check if primary function actually succeeded
            if not self._validate_execution_result(execution_result, instance_info['current_lo_id']):
                error_msg = f"Primary function execution failed for {instance_info['current_lo_id']}: {execution_result}"
                self.logger.error(f"❌ STEP 4 FAILED: {error_msg}")
                self.db.rollback()
                raise ValueError(error_msg)
            
            self.logger.info(f"✅ STEP 4 COMPLETED: Primary function executed successfully: {execution_result}")
            
            # Step 5: Store outputs
            self.logger.info(f"📋 STEP 5: Storing execution outputs...")
            self._store_execution_outputs(
                instance_id, instance_info['current_lo_id'], execution_result
            )
            self.logger.info(f"✅ STEP 5 COMPLETED: Execution outputs stored")
            
            # Step 6: Handle data mappings
            self.logger.info(f"📋 STEP 6: Processing data mappings...")
            self._process_data_mappings(
                instance_id, instance_info['current_lo_id'], instance_info['go_id']
            )
            self.logger.info(f"✅ STEP 6 COMPLETED: Data mappings processed")
            
            # Step 7: Determine next LO
            self.logger.info(f"📋 STEP 7: Resolving next local objective...")
            next_lo_id = self._resolve_next_local_objective(
                instance_info['current_lo_id'], processed_inputs
            )
            self.logger.info(f"✅ STEP 7 COMPLETED: Next LO resolved: {next_lo_id}")
            
            # Step 8: Update workflow instance
            self.logger.info(f"📋 STEP 8: Updating workflow instance...")
            self._update_workflow_instance(instance_id, next_lo_id)
            self.logger.info(f"✅ STEP 8 COMPLETED: Workflow instance updated")
            
            # Step 9: Create individual LO table record
            self.logger.info(f"📋 STEP 9: Creating individual LO execution record...")
            lo_table_name = self._create_lo_execution_record(
                instance_id, instance_info['current_lo_id'], instance_info['go_id'], 
                user_id, processed_inputs, execution_result
            )
            self.logger.info(f"✅ STEP 9 COMPLETED: LO execution record created in table: {lo_table_name}")
            
            # Step 10: Update workflow transaction
            self.logger.info(f"📋 STEP 10: Updating workflow transaction...")
            self._update_workflow_transaction(
                instance_id, instance_info['go_id'], instance_info['current_lo_id'], 
                tenant_id, user_id, processed_inputs, execution_result
            )
            self.logger.info(f"✅ STEP 10 COMPLETED: Workflow transaction updated")
            
            # CRITICAL: Commit the transaction only after ALL operations succeed
            self.db.commit()
            self.logger.info(f"🎉 ALL STEPS COMPLETED SUCCESSFULLY! Transaction committed for {instance_info['current_lo_id']}")
            
            # ADDITIONAL COMMIT: Ensure database operations are persisted immediately
            # This prevents rollback issues when subsequent LOs fail
            try:
                # Get function type for this LO
                function_type_query = "SELECT function_type FROM workflow_runtime.local_objectives WHERE lo_id = :lo_id"
                function_type_result = self.db.execute(text(function_type_query), {"lo_id": instance_info['current_lo_id']}).fetchone()
                
                if function_type_result and function_type_result.function_type.lower() in ['create', 'update', 'delete']:
                    self.db.commit()
                    self.logger.info(f"💾 ADDITIONAL COMMIT: Database operation {function_type_result.function_type} persisted for {instance_info['current_lo_id']}")
            except Exception as commit_error:
                self.logger.error(f"⚠️ Additional commit failed: {str(commit_error)}")
            
            return {
                "status": "Completed",
                "message": f"Local Objective {instance_info['current_lo_id']} executed successfully",
                "output": execution_result,
                "next_lo_id": next_lo_id,
                "execution_id": f"{instance_id}_{instance_info['current_lo_id']}",
                "lo_execution_table": lo_table_name
            }
            
        except Exception as e:
            self.logger.error(f"💥 CRITICAL ERROR in LO execution: {str(e)}")
            try:
                self.db.rollback()
                self.logger.info(f"🔄 Transaction rolled back due to error")
            except Exception as rollback_error:
                self.logger.error(f"💥 ROLLBACK FAILED: {str(rollback_error)}")
            raise
    
    def _check_execution_permissions(self, user_id: str, tenant_id: str, instance_id: str) -> bool:
        """Check if user has permission to execute workflow instances"""
        # Get the current LO ID for this instance
        instance_info = self._get_workflow_instance(instance_id, tenant_id)
        if not instance_info:
            return False
        
        current_lo_id = instance_info.get('current_lo_id')
        if not current_lo_id:
            return False
        
        # CRITICAL: Check if this is a SYSTEM agent type LO - bypass RBAC for system functions
        system_check_query = """
        SELECT agent_type FROM workflow_runtime.local_objectives 
        WHERE lo_id = :lo_id
        """
        
        result = self.db.execute(text(system_check_query), {"lo_id": current_lo_id}).fetchone()
        
        if result and result.agent_type and result.agent_type.upper() == 'SYSTEM':
            self.logger.info(f"🤖 RBAC BYPASS: LO {current_lo_id} is SYSTEM agent type - skipping RBAC checks")
            # Still check tenant access for security
            return self.rbac.check_tenant_access(user_id, tenant_id)
        
        # For non-SYSTEM LOs, check LO-specific permissions
        return (
            self.rbac.check_user_permissions(user_id, tenant_id, current_lo_id, "write") and
            self.rbac.check_tenant_access(user_id, tenant_id)
        )
    
    def _get_workflow_instance(self, instance_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get and validate workflow instance"""
        try:
            query = """
            SELECT wi.instance_id, wi.go_id, wi.current_lo_id, wi.tenant_id, wi.status
            FROM workflow_runtime.workflow_instances wi
            WHERE wi.instance_id = :instance_id AND wi.tenant_id = :tenant_id
            """
            
            result = self.db.execute(text(query), {
                "instance_id": instance_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            if not result:
                return None
            
            if result.status.lower() != "active":
                self.logger.warning(f"Workflow instance {instance_id} is not active (status: {result.status})")
                return None
            
            if not result.current_lo_id:
                self.logger.warning(f"No current LO found for instance {instance_id}")
                return None
            
            return {
                "instance_id": result.instance_id,
                "go_id": result.go_id,
                "current_lo_id": result.current_lo_id,
                "tenant_id": result.tenant_id,
                "status": result.status
            }
            
        except Exception as e:
            self.logger.error(f"Error getting workflow instance: {str(e)}")
            return None
    
    def _process_all_input_types(
        self, 
        instance_id: str, 
        lo_id: str, 
        user_input_data: Dict[str, Any],
        user_id: str
    ) -> Dict[str, Any]:
        """Process inputs based on source type following your specification"""
        
        self.logger.info(f"🔍 DEBUG: Starting input processing for LO {lo_id}")
        self.logger.info(f"🔍 DEBUG: User input data received: {user_input_data}")
        self.logger.info(f"🔍 DEBUG: Instance ID: {instance_id}, User ID: {user_id}")
        
        # CRITICAL: Validate required fields before processing
        validation_result = self._validate_required_inputs(lo_id, user_input_data)
        if not validation_result["is_valid"]:
            error_message = f"Validation Error: {validation_result['error_message']}"
            self.logger.error(f"❌ INPUT VALIDATION FAILED: {error_message}")
            raise ValueError(error_message)
        
        final_inputs = {}
        
        # Get all input fields categorized by type
        self.logger.info(f"🔍 DEBUG: Getting input fields by type...")
        input_fields = self._get_input_fields_by_type(lo_id)
        
        self.logger.info(f"🔍 DEBUG: Input fields categorized:")
        for category, fields in input_fields.items():
            self.logger.info(f"🔍 DEBUG:   {category}: {len(fields)} fields")
            for field in fields:
                self.logger.info(f"🔍 DEBUG:     - {field.get('item_id')} ({field.get('attribute_id')})")
        
        # 1. User inputs - from API request
        self.logger.info(f"🔍 DEBUG: === PROCESSING USER INPUTS ===")
        # Build attribute name mapping for ALL HUMAN fields (including system_dependent ones)
        attr_name_mapping = {}
        
        # Get all fields first
        all_fields = []
        for category, fields in input_fields.items():
            all_fields.extend(fields)
        
        # Get all HUMAN fields for attribute mapping
        human_fields_for_mapping = [f for f in all_fields if f.get('agent_type') == 'HUMAN']
        
        for field in human_fields_for_mapping:
            attr_id = field.get('attribute_id')
            self.logger.info(f"🔍 DEBUG: Getting attribute name for HUMAN field {attr_id}")
            # Get attribute name from entity_attributes table
            attr_name_query = """
            SELECT name FROM workflow_runtime.entity_attributes 
            WHERE attribute_id = :attr_id
            """
            attr_result = self.db.execute(text(attr_name_query), {"attr_id": attr_id}).fetchone()
            if attr_result:
                attr_name_mapping[attr_result.name] = attr_id
                self.logger.info(f"🔍 DEBUG: Mapped HUMAN field {attr_result.name} -> {attr_id}")
            else:
                self.logger.warning(f"🔍 DEBUG: No attribute name found for HUMAN field {attr_id}")
        
        self.logger.info(f"🔍 DEBUG: Complete HUMAN attribute name mapping: {attr_name_mapping}")
        
        # === PRIMARY PROCESSING: AGENT_TYPE BASED (HUMAN vs DIGITAL) ===
        # This is the new primary logic that takes precedence over legacy source_type categorization
        
        # Get all fields and separate by agent_type
        all_fields = []
        for category, fields in input_fields.items():
            all_fields.extend(fields)
        
        # Separate fields by agent_type
        human_fields = [f for f in all_fields if f.get('agent_type') == 'HUMAN']
        digital_fields = [f for f in all_fields if f.get('agent_type') in ['DIGITAL', 'System']]
        fields_without_agent_type = [f for f in all_fields if not f.get('agent_type')]
        
        # Track processed fields to avoid duplicate processing
        processed_fields = set()
        
        self.logger.info(f"🔍 DEBUG: Found {len(human_fields)} HUMAN fields and {len(digital_fields)} DIGITAL fields")
        self.logger.info(f"🔍 DEBUG: HUMAN fields: {[f.get('item_id') for f in human_fields]}")
        self.logger.info(f"🔍 DEBUG: DIGITAL fields: {[f.get('item_id') for f in digital_fields]}")
        
        # 1. Process HUMAN fields (get from user input)
        self.logger.info(f"🔍 DEBUG: === PROCESSING HUMAN FIELDS (USER INPUT) ===")
        for field in human_fields:
            attr_id = field.get('attribute_id')
            item_id = field.get('item_id')
            source_type = field.get('source_type')
            nested_function_id = field.get('nested_function_id')
            
            self.logger.info(f"🔍 DEBUG: Processing HUMAN field {item_id} (attr_id: {attr_id}, source_type: {source_type})")
            
            # Handle system_dependent HUMAN fields (like leaveSubTypeName)
            if source_type == "system_dependent" and nested_function_id:
                self.logger.info(f"🔍 DEBUG: HUMAN field with system_dependent source - treating as user input with nested function fallback")
                
                # First try to get user input
                attr_name = None
                for name, aid in attr_name_mapping.items():
                    if aid == attr_id:
                        attr_name = name
                        break
                
                if attr_name and attr_name in user_input_data:
                    # User provided value - use it
                    value = user_input_data[attr_name]
                    final_inputs[attr_id] = value
                    self.logger.info(f"🔍 DEBUG: Found user input for system_dependent HUMAN field {attr_name} = {value}")
                    self._update_input_execution(instance_id, item_id, value, user_id)
                    self.logger.info(f"🔍 DEBUG: ✅ Processed HUMAN system_dependent input: {attr_name} ({attr_id}) = {value}")
                else:
                    # No user input - execute nested function as fallback
                    self.logger.info(f"🔍 DEBUG: No user input for system_dependent HUMAN field, executing nested function {nested_function_id}")
                    self._populate_nested_function_inputs(nested_function_id, instance_id, user_id)
                    result = self._execute_nested_function(nested_function_id, instance_id)
                    final_inputs[attr_id] = result
                    self._update_input_execution(instance_id, item_id, result, user_id)
                    self._update_nested_function_output(nested_function_id, result, user_id, instance_id)
                    self.logger.info(f"🔍 DEBUG: ✅ Processed HUMAN system_dependent fallback: {nested_function_id} = {result}")
            else:
                # Regular HUMAN field processing
                # Find the attribute name for this attribute_id
                attr_name = None
                for name, aid in attr_name_mapping.items():
                    if aid == attr_id:
                        attr_name = name
                        break
                
                self.logger.info(f"🔍 DEBUG: Resolved attribute name: {attr_name}")
                
                # Check if user provided this input by attribute name
                if attr_name and attr_name in user_input_data:
                    value = user_input_data[attr_name]
                    final_inputs[attr_id] = value
                    self.logger.info(f"🔍 DEBUG: Found user input {attr_name} = {value}")

                    # Update lo_input_execution table
                    self.logger.info(f"🔍 DEBUG: Updating input execution for {item_id}")
                    self._update_input_execution(instance_id, item_id, value, user_id)
                    self.logger.info(f"🔍 DEBUG: ✅ Processed HUMAN input: {attr_name} ({attr_id}) = {value}")
                else:
                    self.logger.warning(f"🔍 DEBUG: ❌ User input not found for HUMAN field {attr_name} (available: {list(user_input_data.keys())})")
                    # CRITICAL FIX: Handle missing user input by using default value or setting to None
                    self._update_input_execution(instance_id, item_id, None, user_id)
                    # Get the actual stored value (which may include default value)
                    stored_value = self._get_input_value(instance_id, field.get('slot_id'))
                    if stored_value is not None:
                        final_inputs[attr_id] = stored_value
                        self.logger.info(f"🔍 DEBUG: ✅ Used default/stored value for HUMAN field {attr_name}: {stored_value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: ⚠️ No value available for required HUMAN field {attr_name}")
        
        # CRITICAL: Process constants and mappings first, then commit before nested functions
        self.logger.info(f"🔍 DEBUG: === PROCESSING DIGITAL FIELDS (CONSTANTS & MAPPINGS FIRST) ===")
        for field in digital_fields:
            attr_id = field.get('attribute_id')
            item_id = field.get('item_id')
            source_type = field.get('source_type')
            nested_function_id = field.get('nested_function_id')
            entity_id = field.get('entity_id')
            
            self.logger.info(f"🔍 DEBUG: Processing DIGITAL field {item_id} (attr_id: {attr_id}, source_type: {source_type})")
            
            # Process constants and mappings first (no nested functions)
            if source_type == "constant":
                # Get constant value
                constant_value = self._get_constant_value(entity_id, attr_id)
                final_inputs[attr_id] = constant_value
                self._update_input_execution(instance_id, item_id, constant_value, user_id)
                self.logger.info(f"🔍 DEBUG: ✅ Processed DIGITAL constant: {attr_id} = {constant_value}")
                
            elif source_type == "mapping":
                # Get mapped value (with default value support)
                mapped_value = self._get_mapped_input_value(instance_id, item_id)
                if mapped_value is not None:
                    final_inputs[attr_id] = mapped_value
                    self._update_input_execution(instance_id, item_id, mapped_value, user_id)
                    self.logger.info(f"🔍 DEBUG: ✅ Processed DIGITAL mapping: {attr_id} = {mapped_value}")
                else:
                    # No mapped value found - use default value in _update_input_execution
                    self._update_input_execution(instance_id, item_id, None, user_id)
                    # CRITICAL FIX: Get the actual stored value (which may include default value)
                    stored_value = self._get_input_value(instance_id, field.get('slot_id'))
                    if stored_value is not None:
                        final_inputs[attr_id] = stored_value
                        self.logger.info(f"🔍 DEBUG: ✅ Used default value for DIGITAL mapping field {item_id}: {stored_value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: ⚠️ No mapped or default value found for DIGITAL field {item_id}")
        
        # CRITICAL: Commit user inputs, constants, and mappings before executing nested functions
        # This prevents transaction rollback from losing input data when nested functions fail
        self.logger.info(f"🔍 DEBUG: === COMMITTING BASIC INPUTS BEFORE NESTED FUNCTION EXECUTION ===")
        try:
            self.db.commit()
            self.logger.info(f"🔍 DEBUG: ✅ Successfully committed user inputs, constants, and mappings")
        except Exception as e:
            self.logger.error(f"🔍 DEBUG: ❌ Failed to commit basic inputs: {str(e)}")
            raise
        
        # Now process nested functions and information fields
        self.logger.info(f"🔍 DEBUG: === PROCESSING DIGITAL FIELDS (NESTED FUNCTIONS) ===")
        for field in digital_fields:
            attr_id = field.get('attribute_id')
            item_id = field.get('item_id')
            source_type = field.get('source_type')
            nested_function_id = field.get('nested_function_id')
            entity_id = field.get('entity_id')
            
            # Only process nested functions and information fields now
            if (source_type == "nested_function" or source_type == "system_dependent") and nested_function_id:
                # Check if this is a business rule nested function - skip if it is
                is_business_rule = self._is_business_rule_function(nested_function_id)
                if is_business_rule:
                    self.logger.info(f"🔍 DEBUG: Skipping business rule nested function {nested_function_id} - will be executed in validation phase")
                    continue
                
                # Execute nested function (non-business rule)
                self.logger.info(f"🔍 DEBUG: Executing nested function {nested_function_id}")
                self._populate_nested_function_inputs(nested_function_id, instance_id, user_id)
                result = self._execute_nested_function(nested_function_id, instance_id)
                
                # CRITICAL: Check if nested function failed
                if result is None:
                    error_msg = f"Nested function {nested_function_id} failed to execute properly"
                    self.logger.error(f"❌ NESTED FUNCTION ERROR: {error_msg}")
                    raise ValueError(error_msg)
                
                final_inputs[attr_id] = result
                self._update_input_execution(instance_id, item_id, result, user_id)
                self._update_nested_function_output(nested_function_id, result, user_id, instance_id)
                self.logger.info(f"🔍 DEBUG: ✅ Processed DIGITAL nested function: {nested_function_id} = {result}")
                    
            elif source_type == "information":
                # Information field - get predefined value
                if nested_function_id:
                    info_value = self._execute_nested_function(nested_function_id, instance_id)
                    final_inputs[attr_id] = info_value
                    self._update_input_execution(instance_id, item_id, info_value, user_id)
                    self.logger.info(f"🔍 DEBUG: ✅ Processed DIGITAL information (function): {attr_id} = {info_value}")
                else:
                    # Use default value for information field
                    self._update_input_execution(instance_id, item_id, None, user_id)
                    self.logger.info(f"🔍 DEBUG: ✅ Processed DIGITAL information (default): {attr_id}")
                    
            elif source_type not in ["constant", "mapping"]:
                self.logger.warning(f"🔍 DEBUG: ❌ Unknown DIGITAL source_type: {source_type} for {item_id}")
        
        # Mark processed fields to avoid duplicate processing
        for field in human_fields + digital_fields:
            processed_fields.add(field.get('item_id'))
        
        # NEW STEP: Execute Business Rules Validation
        self.logger.info(f"🔍 DEBUG: === BUSINESS RULES VALIDATION ===")
        try:
            business_rules_validator = BusinessRulesValidator(self.db)
            validation_result = business_rules_validator.validate_business_rules(
                lo_id, instance_id, final_inputs, user_id
            )
            
            if not validation_result.get("validation_passed", True):
                # Business rule validation failed - raise exception to stop execution
                error_message = validation_result.get("primary_error", "Business rule validation failed")
                self.logger.error(f"🔍 BUSINESS RULES: Validation failed: {error_message}")
                raise ValueError(f"Business rule validation failed: {error_message}")
            
            self.logger.info(f"🔍 BUSINESS RULES: All validations passed successfully")
            
        except Exception as e:
            self.logger.error(f"🔍 BUSINESS RULES: Validation error: {str(e)}")
            # Re-raise the exception to stop LO execution
            raise
        
        self.logger.info(f"🔍 DEBUG: === FINAL PROCESSED INPUTS ===")
        self.logger.info(f"🔍 DEBUG: Final inputs dictionary: {final_inputs}")
        self.logger.info(f"🔍 DEBUG: Total inputs processed: {len(final_inputs)}")

        # CRITICAL DEBUG: Verify all stored values in database
        self._debug_verify_stored_values(instance_id, lo_id)

        return final_inputs

    def _debug_verify_stored_values(self, instance_id: str, lo_id: str):
        """Debug method to verify what values are actually stored in the database"""
        try:
            self.logger.info(f"🔍 DEBUG: === VERIFYING STORED VALUES IN DATABASE ===")

            # Get all stored input values for this instance and LO
            verify_query = """
            SELECT
                lie.input_contextual_id,
                lie.input_value,
                lii.attribute_id,
                ea.name as attribute_name,
                lii.source_type,
                lii.agent_type,
                lii.default_value
            FROM workflow_runtime.lo_input_execution lie
            JOIN workflow_runtime.lo_input_items lii ON lie.input_contextual_id = lii.slot_id
            LEFT JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
            WHERE lie.instance_id = :instance_id AND lie.lo_id = :lo_id
            ORDER BY ea.name
            """

            results = self.db.execute(text(verify_query), {
                "instance_id": instance_id,
                "lo_id": lo_id
            }).fetchall()

            null_count = 0
            total_count = len(results)

            for result in results:
                value_status = "NULL" if result.input_value is None else "HAS_VALUE"
                if result.input_value is None:
                    null_count += 1

                self.logger.info(f"🔍 DEBUG: {result.attribute_name or 'UNKNOWN'} ({result.attribute_id}) = {result.input_value} [{value_status}] (source: {result.source_type}, agent: {result.agent_type}, default: {result.default_value})")

            self.logger.info(f"🔍 DEBUG: === SUMMARY: {null_count}/{total_count} fields have NULL values ===")

            if null_count > 0:
                self.logger.warning(f"⚠️ WARNING: {null_count} fields have NULL values in database!")

        except Exception as e:
            self.logger.error(f"Error in debug verification: {str(e)}")
    
    def _validate_required_inputs(self, lo_id: str, user_input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that all required fields are provided in user input data"""
        try:
            self.logger.info(f"🔍 VALIDATION: Starting required input validation for LO {lo_id}")
            self.logger.info(f"🔍 VALIDATION: User input data: {user_input_data}")
            
            # Get all required HUMAN fields for this LO
            required_fields_query = """
            SELECT lii.item_id, lii.required, lii.attribute_id, ea.name as attribute_name
            FROM workflow_runtime.lo_input_items lii
            JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
            WHERE lii.lo_id = :lo_id 
            AND lii.agent_type = 'HUMAN' 
            AND lii.required = true
            """
            
            required_fields = self.db.execute(text(required_fields_query), {"lo_id": lo_id}).fetchall()
            
            self.logger.info(f"🔍 VALIDATION: Found {len(required_fields)} required HUMAN fields")
            
            missing_fields = []
            
            for field in required_fields:
                attribute_name = field.attribute_name
                item_id = field.item_id
                
                self.logger.info(f"🔍 VALIDATION: Checking required field: {attribute_name} ({item_id})")
                
                # Check if this required field is provided in user input
                if attribute_name not in user_input_data:
                    missing_fields.append(attribute_name)
                    self.logger.warning(f"🔍 VALIDATION: Missing required field: {attribute_name}")
                elif user_input_data[attribute_name] is None or user_input_data[attribute_name] == "":
                    missing_fields.append(attribute_name)
                    self.logger.warning(f"🔍 VALIDATION: Required field {attribute_name} is null/empty")
                else:
                    self.logger.info(f"🔍 VALIDATION: Required field {attribute_name} is provided: {user_input_data[attribute_name]}")
            
            if missing_fields:
                error_message = f"Missing required fields: {', '.join(missing_fields)}"
                self.logger.error(f"🔍 VALIDATION: {error_message}")
                return {
                    "is_valid": False,
                    "error_message": error_message,
                    "missing_fields": missing_fields
                }
            
            self.logger.info(f"🔍 VALIDATION: All required fields are provided")
            return {
                "is_valid": True,
                "error_message": None,
                "missing_fields": []
            }
            
        except Exception as e:
            self.logger.error(f"🔍 VALIDATION: Error during validation: {str(e)}")
            return {
                "is_valid": False,
                "error_message": f"Validation error: {str(e)}",
                "missing_fields": []
            }
    
    def _get_input_fields_by_type(self, lo_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Get input fields categorized by source type"""
        try:
            query = """
            SELECT id, item_id, source_type, nested_function_id, entity_id, attribute_id,
                   constant_field, information_field, agent_type
            FROM workflow_runtime.lo_input_items
            WHERE lo_id = :lo_id
            """
            
            results = self.db.execute(text(query), {"lo_id": lo_id}).fetchall()
            
            categorized = {
                'user': [],
                'nested_function': [],
                'constant': [],
                'information': [],
                'mapping': []
            }
            
            for row in results:
                field_data = {
                    'id': row.id,
                    'item_id': row.item_id,
                    'source_type': row.source_type,
                    'nested_function_id': row.nested_function_id,
                    'entity_id': row.entity_id,
                    'attribute_id': row.attribute_id,
                    'agent_type': row.agent_type
                }
                
                source_type = row.source_type.lower() if row.source_type else 'user'
                
                # Prioritize explicit source_type over field flags
                if source_type == 'mapping':
                    categorized['mapping'].append(field_data)
                elif source_type == 'user':
                    categorized['user'].append(field_data)
                elif source_type == 'nested_function':
                    categorized['nested_function'].append(field_data)
                elif source_type == 'constant' or row.constant_field:
                    categorized['constant'].append(field_data)
                elif source_type == 'information' or row.information_field:
                    categorized['information'].append(field_data)
                elif row.nested_function_id:
                    # Only check nested_function_id if source_type is not explicitly set
                    categorized['nested_function'].append(field_data)
                else:
                    # Default to user input
                    categorized['user'].append(field_data)
            
            return categorized
            
        except Exception as e:
            self.logger.error(f"Error categorizing input fields: {str(e)}")
            return {'user': [], 'nested_function': [], 'constant': [], 'information': [], 'mapping': []}
    
    def _execute_nested_function(self, nested_function_id: str, instance_id: str) -> Any:
        """Execute nested function with context data using V2 adapter for better parameter handling"""
        try:
            self.logger.info(f"🔍 DEBUG: Executing nested function {nested_function_id}")
            
            # Get function definition
            query = """
            SELECT function_name, function_type, parameters 
            FROM workflow_runtime.lo_nested_functions 
            WHERE nested_function_id = :nested_function_id
            """
            
            result = self.db.execute(text(query), {"nested_function_id": nested_function_id}).fetchone()
            
            if not result:
                self.logger.error(f"🔍 DEBUG: Nested function {nested_function_id} not found")
                return None
            
            function_name = result.function_name
            self.logger.info(f"🔍 DEBUG: Found function: {function_name}")
            
            # Get nested function input parameters from lo_nested_function_input_items
            params_query = """
            SELECT item_id, slot_id, entity_id, attribute_id, value, data_type
            FROM workflow_runtime.lo_nested_function_input_items
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            
            param_results = self.db.execute(text(params_query), {"nested_function_id": nested_function_id}).fetchall()
            
            # Build parameters for function execution
            execution_params = {}

            # Process each parameter - READ ONLY FROM INPUT STACK
            for param_row in param_results:
                slot_id = param_row.slot_id
                entity_id = param_row.entity_id
                attribute_id = param_row.attribute_id
                default_value = param_row.value
                
                self.logger.info(f"🔍 DEBUG: Processing parameter {slot_id} (entity: {entity_id}, attr: {attribute_id})")
                
                # CRITICAL: Get value using attribute_id when available, fallback to slot_id
                param_value = None
                
                # Use attribute_id if available, otherwise use slot_id
                lookup_key = slot_id if slot_id else None
                
                if lookup_key:
                    # Get value from NESTED FUNCTION input execution table (proper flow)
                    input_value_query = """
                    SELECT input_value FROM workflow_runtime.lo_nested_function_input_execution
                    WHERE instance_id = :instance_id 
                    AND nested_function_id = :nested_function_id 
                    AND input_contextual_id = :lookup_key
                    ORDER BY created_at DESC LIMIT 1
                    """

                    input_result = self.db.execute(text(input_value_query), {
                        "instance_id": instance_id,
                        "nested_function_id": nested_function_id,
                        "lookup_key": lookup_key
                    }).fetchone()

                    
                    if input_result and input_result.input_value:
                        import json
                        try:
                            param_value = json.loads(input_result.input_value)
                            self.logger.info(f"🔍 DEBUG: Found parameter value in input stack: {lookup_key} = {param_value}")
                        except (json.JSONDecodeError, TypeError):
                            param_value = input_result.input_value
                            self.logger.info(f"🔍 DEBUG: Found parameter value (raw) in input stack: {lookup_key} = {param_value}")
                    elif default_value:
                        param_value = default_value
                        self.logger.info(f"🔍 DEBUG: Using default parameter value: {lookup_key} = {param_value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: No value found in input stack for {lookup_key}")
                
                # CRITICAL: Use attribute_id to get the contextual_id for parameter lookup
                if entity_id and attribute_id:
                    # Get attribute name from database as-is
                    column_query = "SELECT name FROM workflow_runtime.entity_attributes WHERE attribute_id = :attribute_id"
                    column_result = self.db.execute(text(column_query), {"attribute_id": attribute_id}).fetchone()
                    
                    if column_result:
                        # Use the exact attribute name from database without any conversion
                        attribute_name = column_result.name
                        
                        # CRITICAL: If param_value is None, try to get it using attribute_id mapping
                        if param_value is None and attribute_id:
                            # Get the contextual_id for this attribute_id from lo_input_items
                            contextual_query = """
                            SELECT slot_id FROM workflow_runtime.lo_input_items
                            WHERE attribute_id = :attribute_id
                            AND lo_id = (SELECT lo_id FROM workflow_runtime.lo_nested_functions WHERE nested_function_id = :nested_function_id)
                            LIMIT 1
                            """
                            contextual_result = self.db.execute(text(contextual_query), {
                                "attribute_id": attribute_id,
                                "nested_function_id": nested_function_id
                            }).fetchone()
                            
                            if contextual_result:
                                contextual_id = contextual_result.slot_id
                                # Get value from lo_input_execution using contextual_id
                                value_query = """
                                SELECT input_value FROM workflow_runtime.lo_input_execution
                                WHERE instance_id = :instance_id 
                                AND input_contextual_id = :contextual_id
                                ORDER BY created_at DESC LIMIT 1
                                """
                                value_result = self.db.execute(text(value_query), {
                                    "instance_id": instance_id,
                                    "contextual_id": contextual_id
                                }).fetchone()
                                
                                if value_result and value_result.input_value:
                                    import json
                                    try:
                                        param_value = json.loads(value_result.input_value)
                                        self.logger.info(f"🔍 DEBUG: Found value using attribute mapping {attribute_id} -> {contextual_id}: {param_value}")
                                    except (json.JSONDecodeError, TypeError):
                                        param_value = value_result.input_value
                                        self.logger.info(f"🔍 DEBUG: Found raw value using attribute mapping {attribute_id} -> {contextual_id}: {param_value}")
                                else:
                                    self.logger.warning(f"🔍 DEBUG: No value found for contextual_id {contextual_id}")
                            else:
                                self.logger.warning(f"🔍 DEBUG: No contextual_id found for attribute_id {attribute_id}")
                        
                        execution_params[attribute_name] = param_value
                        execution_params["entity"] = entity_id
                        execution_params["attribute"] = attribute_id
                        
                        self.logger.info(f"🔍 DEBUG: Added parameter from DB as-is: {attribute_name} = {param_value} (entity: {entity_id})")
                    else:
                        self.logger.warning(f"🔍 DEBUG: Could not find attribute name for {attribute_id}")
                        # Fallback to slot_id if attribute lookup fails
                        if slot_id:
                            simple_name = slot_id.split(".")[-1] if "." in slot_id else slot_id
                            execution_params[simple_name] = param_value
                            self.logger.info(f"🔍 DEBUG: Fallback parameter: {simple_name} = {param_value}")
                else:
                    # Fallback for parameters without entity/attribute
                    if slot_id:
                        simple_name = slot_id.split(".")[-1] if "." in slot_id else slot_id
                        execution_params[simple_name] = param_value
                        self.logger.info(f"🔍 DEBUG: Simple parameter: {simple_name} = {param_value}")

            

            
            self.logger.info(f"🔍 DEBUG: Final execution parameters: {execution_params}")
            
            # Execute using V2 adapter only
            self.logger.info(f"🔧 V2: Executing {function_name} using V2 adapter")
            from app.services.v2.v2_adapter import execute_nested_function_v2
            
            result_value = execute_nested_function_v2(
                function_name=function_name,
                db_session=self.db,
                input_values=execution_params,
                nested_function_id=nested_function_id
            )
            
            self.logger.info(f"✅ V2: Function {function_name} executed: {result_value}")
            return result_value
            
        except Exception as e:
            self.logger.error(f"🔍 DEBUG: Error executing nested function {nested_function_id}: {str(e)}")
            return None
        

    def _populate_nested_function_inputs(self, nested_function_id: str, instance_id: str, user_id: str = None):
        """
        Populate lo_nested_function_input_execution table with input values from lo_input_execution.
        This ensures nested functions read their inputs from the proper input stack.
        """
        try:
            self.logger.info(f"🔍 DEBUG: Populating nested function inputs for {nested_function_id}")
            
            # Get nested function input parameter definitions
            params_query = """
            SELECT item_id, slot_id, entity_id, attribute_id, value, data_type
            FROM workflow_runtime.lo_nested_function_input_items
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            
            param_results = self.db.execute(text(params_query), {"nested_function_id": nested_function_id}).fetchall()
            
            for param_row in param_results:
                slot_id = param_row.slot_id
                item_id = param_row.item_id
                
                self.logger.info(f"🔍 DEBUG: Processing nested function input {item_id} (slot: {slot_id})")
                
                # Get the corresponding value from lo_input_execution OR use constant value
                input_value = None
                
                # FIRST: Check if this parameter has a constant value in the input items table
                if param_row.value is not None:
                    input_value = param_row.value
                    self.logger.info(f"🔍 DEBUG: Using constant value for {slot_id}: {input_value}")
                elif slot_id:
                    # CRITICAL FIX: PRIORITIZE mapped values from lo_nested_function_input_execution
                    # Check if there's a mapped value for this nested function and slot_id
                    mapped_value_query = """
                    SELECT input_value FROM workflow_runtime.lo_nested_function_input_execution
                    WHERE instance_id = :instance_id 
                    AND nested_function_id = :nested_function_id
                    AND input_contextual_id = :slot_id
                    ORDER BY created_at DESC LIMIT 1
                    """
                    
                    mapped_result = self.db.execute(text(mapped_value_query), {
                        "instance_id": instance_id,
                        "nested_function_id": nested_function_id,
                        "slot_id": slot_id
                    }).fetchone()
                    
                    if mapped_result and mapped_result.input_value:
                        import json
                        try:
                            input_value = json.loads(mapped_result.input_value)
                            self.logger.info(f"🔍 DEBUG: Found MAPPED value for {slot_id}: {input_value}")
                        except (json.JSONDecodeError, TypeError):
                            input_value = mapped_result.input_value
                            self.logger.info(f"🔍 DEBUG: Found MAPPED value (raw) for {slot_id}: {input_value}")
                    else:
                        # FALLBACK: Try to get value from lo_input_execution for dynamic parameters
                        # Get the LO ID for this nested function
                        nf_lo_query = """
                        SELECT lo_id FROM workflow_runtime.lo_nested_functions 
                        WHERE nested_function_id = :nested_function_id
                        """
                        nf_lo_result = self.db.execute(text(nf_lo_query), {
                            "nested_function_id": nested_function_id
                        }).fetchone()
                        
                        if nf_lo_result:
                            lo_id = nf_lo_result.lo_id
                            
                            input_value_query = """
                            SELECT input_value FROM workflow_runtime.lo_input_execution
                            WHERE instance_id = :instance_id 
                            AND input_contextual_id = :slot_id 
                            AND lo_id = :lo_id
                            ORDER BY created_at DESC LIMIT 1
                            """
                            
                            input_result = self.db.execute(text(input_value_query), {
                                "instance_id": instance_id,
                                "slot_id": slot_id,
                                "lo_id": lo_id
                            }).fetchone()
                        else:
                            self.logger.error(f"🔍 DEBUG: Could not find LO ID for nested function {nested_function_id}")
                            input_result = None
                        
                        if input_result and input_result.input_value:
                            input_value = input_result.input_value
                            self.logger.info(f"🔍 DEBUG: Found FALLBACK input value for {slot_id}: {input_value}")
                        else:
                            self.logger.warning(f"🔍 DEBUG: No input value found for {slot_id}")
                else:
                    self.logger.warning(f"🔍 DEBUG: No slot_id and no constant value for parameter {item_id}")
                
                # Insert/Update the nested function input execution record
                self.logger.info(f"🔍 DEBUG: About to insert nested function input: {nested_function_id}.{slot_id} = {input_value}")

                upsert_query = """
                INSERT INTO workflow_runtime.lo_nested_function_input_execution 
                (instance_id, nested_function_id, input_contextual_id, input_value, created_at, created_by, updated_at, updated_by)
                VALUES (:instance_id, :nested_function_id, :input_contextual_id, :input_value, NOW(), :user_id, NOW(), :user_id)
                ON CONFLICT (instance_id, nested_function_id, input_contextual_id) 
                DO UPDATE SET 
                    input_value = EXCLUDED.input_value, 
                    updated_at = NOW(), 
                    updated_by = EXCLUDED.updated_by
                """
                
                # JSON encode the input_value for JSONB field
                import json
                if input_value is not None:
                    try:
                        json_encoded_value = json.dumps(input_value)
                    except (TypeError, ValueError):
                        json_encoded_value = json.dumps(str(input_value))
                else:
                    json_encoded_value = None
                    
                self.db.execute(text(upsert_query), {
                    "instance_id": instance_id,
                    "nested_function_id": nested_function_id,
                    "input_contextual_id": slot_id,
                    "input_value": json_encoded_value,
                    "user_id": user_id
                })

                
                self.logger.info(f"🔍 DEBUG: ✅ Populated nested function input: {nested_function_id}.{slot_id} = {input_value}")
                
        except Exception as e:
            self.logger.error(f"❌ Error populating nested function inputs for {nested_function_id}: {str(e)}")

    
    def _update_input_execution(self, instance_id: str, item_id: str, value: Any, user_id: str = None):
        """Update lo_input_execution table with proper audit fields and default value handling"""
        try:
            # First get the contextual_id and default_value for this item
            contextual_query = """
            SELECT slot_id, lo_id, default_value FROM workflow_runtime.lo_input_items WHERE item_id = :item_id
            """
            contextual_result = self.db.execute(text(contextual_query), {"item_id": item_id}).fetchone()
            
            if not contextual_result:
                self.logger.warning(f"No contextual ID found for item {item_id}")
                return
            
            contextual_id = contextual_result.slot_id
            lo_id = contextual_result.lo_id
            default_value = contextual_result.default_value
            
            # Use default value if no value provided
            final_value = value
            if final_value is None and default_value is not None:
                final_value = default_value
                self.logger.info(f"🔍 DEBUG: Using default value for {item_id}: {default_value}")
            elif final_value is None:
                self.logger.warning(f"🔍 DEBUG: No value or default available for {item_id} - storing NULL")
            
            # Serialize value with proper datetime/decimal handling
            import json
            from datetime import datetime, date
            from decimal import Decimal
            
            def json_serializer(obj):
                if isinstance(obj, (datetime, date)):
                    return obj.isoformat()
                elif isinstance(obj, Decimal):
                    return float(obj)
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            if final_value is not None:
                try:
                    serialized_value = json.dumps(final_value, default=json_serializer)
                except (TypeError, ValueError) as e:
                    self.logger.error(f"JSON serialization failed for {item_id}: {str(e)}")
                    # Fallback to string representation
                    serialized_value = json.dumps(str(final_value))
            else:
                serialized_value = None
            
            # Update existing record (rows are pre-created during instance creation)
            update_query = """
            UPDATE workflow_runtime.lo_input_execution 
            SET input_value = :value, 
                updated_at = NOW(), 
                updated_by = :user_id
            WHERE instance_id = :instance_id 
            AND lo_id = :lo_id 
            AND input_contextual_id = :contextual_id
            """
            self.logger.info(f"🔍 DEBUG: About to execute update query with params: {{'instance_id': '{instance_id}', 'lo_id': '{lo_id}', 'contextual_id': '{contextual_id}', 'value': '{serialized_value}', 'user_id': '{user_id}'}}")

            rows_updated = self.db.execute(text(update_query), {
                "instance_id": instance_id,
                "lo_id": lo_id,
                "contextual_id": contextual_id,
                "value": serialized_value,
                "user_id": user_id
            }).rowcount
            
            self.logger.info(f"Updated input execution for {item_id} with value: {final_value} ({rows_updated} rows updated)")

            self.logger.info(f"🔍 DEBUG: Update query executed successfully")

            # CRITICAL DEBUG: Verify what was actually stored
            verify_query = """
            SELECT input_value FROM workflow_runtime.lo_input_execution
            WHERE instance_id = :instance_id AND lo_id = :lo_id AND input_contextual_id = :contextual_id
            """
            verify_result = self.db.execute(text(verify_query), {
                "instance_id": instance_id,
                "lo_id": lo_id,
                "contextual_id": contextual_id
            }).fetchone()

            if verify_result:
                self.logger.info(f"🔍 DEBUG: VERIFICATION - Stored value in DB: {verify_result.input_value}")
            else:
                self.logger.error(f"🔍 DEBUG: VERIFICATION FAILED - No record found after update!")
    
            
        except Exception as e:
            self.logger.error(f"Error updating input execution: {str(e)}")
    
    def _update_nested_function_output(self, nested_function_id: str, result: Any, user_id: str = None, instance_id: str = None):
        """Update nested function output execution and map to target input items"""
        try:
            self.logger.info(f"🔍 DEBUG: Updating nested function output for {nested_function_id} with result: {result}")
            
            # 1. Update the nested function output in lo_nested_function_output_execution (records already exist)
            import json
            
            # Get ALL output item IDs for this nested function (future-proofing for multiple outputs)
            output_items_query = """
            SELECT item_id, slot_id FROM workflow_runtime.lo_nested_function_output_items 
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            output_items_result = self.db.execute(text(output_items_query), {
                "nested_function_id": nested_function_id
            }).fetchall()
            
            if not output_items_result:
                self.logger.error(f"🔍 DEBUG: No output items found for {nested_function_id}")
                return
            
            self.logger.info(f"🔍 DEBUG: Found {len(output_items_result)} output items for {nested_function_id}")
            
            # Update each output item with extracted field values
            total_rows_updated = 0
            for output_item in output_items_result:
                output_contextual_id = output_item.item_id
                slot_id = output_item.slot_id
                
                self.logger.info(f"🔍 DEBUG: Updating output item: {output_contextual_id} (slot: {slot_id})")
                
                # Extract specific field value for this output item
                field_value = self._extract_field_value_from_result(result, slot_id, output_contextual_id)
                
                self.logger.info(f"🔍 DEBUG: FIELD EXTRACTION: slot_id={slot_id}, extracted_value={field_value}, original_result_type={type(result)}")
                
                # UPDATE existing record (records should already exist from workflow instance creation)
                # PRESERVE existing entity_id and attribute_id - don't overwrite them!
                output_update_query = """
                UPDATE workflow_runtime.lo_nested_function_output_execution 
                SET output_value = :output_value, 
                    execution_status = 'success',
                    updated_at = NOW(), 
                    updated_by = :user_id
                WHERE instance_id = :instance_id 
                AND nested_function_id = :nested_function_id 
                AND output_contextual_id = :output_contextual_id
                """
                
                rows_updated = self.db.execute(text(output_update_query), {
                    "nested_function_id": nested_function_id,
                    "instance_id": instance_id,
                    "output_contextual_id": output_contextual_id,
                    "output_value": json.dumps(field_value),
                    "user_id": user_id
                }).rowcount
                
                total_rows_updated += rows_updated
                self.logger.info(f"🔍 DEBUG: Updated {rows_updated} records for output item {output_contextual_id} with value: {field_value}")
            
            self.logger.info(f"🔍 DEBUG: Total updated {total_rows_updated} nested function output records")
            
            # 2. Get mappings for this nested function to target nested function input items
            mapping_query = """
            SELECT source_output_item_id, target_input_item_id 
            FROM workflow_runtime.lo_nested_function_mappings 
            WHERE nested_function_id = :nested_function_id 
            AND target_input_item_id IS NOT NULL
            """
            
            mappings = self.db.execute(text(mapping_query), {
                "nested_function_id": nested_function_id
            }).fetchall()
            
            self.logger.info(f"🔍 DEBUG: Found {len(mappings)} mappings for {nested_function_id}")
            
            # 3. Map extracted field values to target nested function input stacks
            for mapping in mappings:
                source_output_item_id = mapping.source_output_item_id
                target_input_item_id = mapping.target_input_item_id
                
                self.logger.info(f"🔍 DEBUG: Processing mapping {source_output_item_id} -> {target_input_item_id}")
                
                # Get the extracted field value from the output execution table
                output_value_query = """
                SELECT output_value FROM workflow_runtime.lo_nested_function_output_execution
                WHERE instance_id = :instance_id 
                AND nested_function_id = :nested_function_id
                AND output_contextual_id = :source_output_item_id
                AND output_value IS NOT NULL
                ORDER BY created_at DESC LIMIT 1
                """
                
                output_result = self.db.execute(text(output_value_query), {
                    "instance_id": instance_id,
                    "nested_function_id": nested_function_id,
                    "source_output_item_id": source_output_item_id
                }).fetchone()
                
                if output_result and output_result.output_value:
                    import json
                    try:
                        extracted_value = json.loads(output_result.output_value)
                    except (json.JSONDecodeError, TypeError):
                        extracted_value = output_result.output_value
                    
                    self.logger.info(f"🔍 DEBUG: Found extracted value for {source_output_item_id}: {extracted_value}")
                    
                    # Get the target nested function ID and slot_id from the target_input_item_id
                    target_parts = target_input_item_id.split('.')
                    if len(target_parts) >= 3:
                        target_nested_function_id = f"{target_parts[0]}.{target_parts[1]}.{target_parts[2]}"  # GO1.LO6.NF3
                        
                        # Get the slot_id for the target input item
                        target_slot_query = """
                        SELECT slot_id FROM workflow_runtime.lo_nested_function_input_items
                        WHERE item_id = :target_input_item_id
                        """
                        
                        target_slot_result = self.db.execute(text(target_slot_query), {
                            "target_input_item_id": target_input_item_id
                        }).fetchone()
                        
                        if target_slot_result:
                            target_slot_id = target_slot_result.slot_id
                            
                            # Update the target nested function's input execution with the extracted value
                            upsert_target_query = """
                            INSERT INTO workflow_runtime.lo_nested_function_input_execution 
                            (instance_id, nested_function_id, input_contextual_id, input_value, created_at, created_by, updated_at, updated_by)
                            VALUES (:instance_id, :target_nested_function_id, :target_slot_id, :input_value, NOW(), :user_id, NOW(), :user_id)
                            ON CONFLICT (instance_id, nested_function_id, input_contextual_id) 
                            DO UPDATE SET 
                                input_value = EXCLUDED.input_value, 
                                updated_at = NOW(), 
                                updated_by = EXCLUDED.updated_by
                            """
                            
                            # JSON encode the extracted value
                            json_encoded_value = json.dumps(extracted_value)
                            
                            self.db.execute(text(upsert_target_query), {
                                "instance_id": instance_id,
                                "target_nested_function_id": target_nested_function_id,
                                "target_slot_id": target_slot_id,
                                "input_value": json_encoded_value,
                                "user_id": user_id
                            })
                            
                            self.logger.info(f"🔍 DEBUG: ✅ Mapped extracted value {extracted_value} from {source_output_item_id} to {target_nested_function_id}.{target_slot_id}")
                        else:
                            self.logger.warning(f"🔍 DEBUG: Could not find slot_id for target input item {target_input_item_id}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: Invalid target input item format: {target_input_item_id}")
                else:
                    self.logger.warning(f"🔍 DEBUG: No extracted value found for {source_output_item_id}")
            
        except Exception as e:
            self.logger.error(f"Error updating nested function output: {str(e)}")
    
    def _get_constant_value(self, entity_id: str, attribute_id: str) -> Any:
        """Get constant value from constants table"""
        try:
            # Constants are in the constants table
            query = """
            SELECT value FROM workflow_runtime.constants 
            WHERE attribute = :attribute_id AND status = 'active'
            LIMIT 1
            """
            
            result = self.db.execute(text(query), {"attribute_id": attribute_id}).fetchone()
            if result:
                self.logger.info(f"🔍 DEBUG: Found constant value for {attribute_id}: {result.value}")
                return result.value
            else:
                self.logger.warning(f"🔍 DEBUG: No constant found for attribute {attribute_id}")
                return None
            
        except Exception as e:
            self.logger.error(f"Error getting constant value: {str(e)}")
            return None
    
    def _is_business_rule_function(self, nested_function_id: str) -> bool:
        """Check if a nested function is a business rule function"""
        try:
            query = """
            SELECT function_type FROM workflow_runtime.lo_nested_functions 
            WHERE nested_function_id = :nested_function_id
            """
            
            result = self.db.execute(text(query), {"nested_function_id": nested_function_id}).fetchone()
            
            if result and result.function_type:
                return result.function_type.upper() == 'BUSINESS_RULE'
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking if function is business rule: {str(e)}")
            return False
    
    def _get_mapped_input_value(self, instance_id: str, item_id: str) -> Any:
        """Get mapped input value from lo_input_execution using contextual_id lookup"""
        try:
            # CRITICAL FIX: Get the contextual_id (slot_id) for this item_id first
            contextual_query = """
            SELECT slot_id FROM workflow_runtime.lo_input_items 
            WHERE item_id = :item_id
            """
            contextual_result = self.db.execute(text(contextual_query), {"item_id": item_id}).fetchone()
            
            if not contextual_result:
                self.logger.warning(f"🔍 DEBUG: No contextual_id found for item {item_id}")
                return None
            
            contextual_id = contextual_result.slot_id
            
            # Now get the mapped value using contextual_id
            query = """
            SELECT input_value FROM workflow_runtime.lo_input_execution
            WHERE instance_id = :instance_id AND input_contextual_id = :contextual_id
            ORDER BY created_at DESC LIMIT 1
            """
            
            result = self.db.execute(text(query), {
                "instance_id": instance_id,
                "contextual_id": contextual_id
            }).fetchone()
            
            if result and result.input_value:
                import json
                try:
                    parsed_value = json.loads(result.input_value)
                    self.logger.info(f"🔍 DEBUG: Found mapped value for {item_id} ({contextual_id}): {parsed_value}")
                    return parsed_value
                except (json.JSONDecodeError, TypeError):
                    self.logger.info(f"🔍 DEBUG: Found mapped value (raw) for {item_id} ({contextual_id}): {result.input_value}")
                    return result.input_value
            else:
                self.logger.warning(f"🔍 DEBUG: No mapped value found for {item_id} with contextual_id {contextual_id}")
                return None
            
        except Exception as e:
            self.logger.error(f"Error getting mapped input value: {str(e)}")
            return None
    
    def _validate_execution_result(self, execution_result: Dict[str, Any], lo_id: str) -> bool:
        """Validate if the execution result indicates actual success or failure"""
        try:
            if execution_result is None:
                self.logger.error(f"❌ VALIDATION: Execution result is None for {lo_id}")
                return False
            
            if not isinstance(execution_result, dict):
                self.logger.error(f"❌ VALIDATION: Execution result is not a dict for {lo_id}: {type(execution_result)}")
                return False
            
            # Check for explicit error status
            status = execution_result.get("status", "").lower()
            if status in ["error", "failed", "failure"]:
                self.logger.error(f"❌ VALIDATION: Execution failed with status '{status}' for {lo_id}")
                return False
            
            # Check for operation results with rows affected
            rows_affected = execution_result.get("rows_affected")
            if rows_affected is not None:
                if isinstance(rows_affected, (int, str)):
                    rows_count = int(rows_affected)
                    self.logger.info(f"✅ VALIDATION: Operation affected {rows_count} rows for {lo_id}")
            
            # Check for successful statuses
            if status in ["created", "updated", "deleted", "success", "completed"]:
                self.logger.info(f"✅ VALIDATION: Execution successful with status '{status}' for {lo_id}")
                return True
            
            # If no explicit status, check for result data
            if execution_result.get("result") is not None:
                self.logger.info(f"✅ VALIDATION: Execution has result data for {lo_id}")
                return True
            
            # Default case - if we get here, it's unclear
            self.logger.warning(f"⚠️ VALIDATION: Unclear execution result for {lo_id}: {execution_result}")
            return True  # Be optimistic for backward compatibility
            
        except Exception as e:
            self.logger.error(f"❌ VALIDATION: Error validating execution result for {lo_id}: {str(e)}")
            return False
    
    def _execute_primary_function(self, lo_id: str, processed_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the primary function of the local objective using V2 system functions"""
        try:
            self.logger.info(f"🔍 DEBUG: Executing primary function for LO {lo_id} using V2 system")
            self.logger.info(f"🔍 DEBUG: Raw processed inputs: {processed_inputs}")
            
            # Get LO function type and entity
            query = """
            SELECT function_type FROM workflow_runtime.local_objectives WHERE lo_id = :lo_id
            """
            
            result = self.db.execute(text(query), {"lo_id": lo_id}).fetchone()
            
            if not result:
                raise ValueError(f"Local objective {lo_id} not found")
            
            function_type = result.function_type.lower()
            self.logger.info(f"🔍 DEBUG: Function type: {function_type}")
            
            # Get entity for this LO
            entity_query = """
            SELECT DISTINCT entity_id FROM workflow_runtime.lo_input_items WHERE lo_id = :lo_id LIMIT 1
            """
            entity_result = self.db.execute(text(entity_query), {"lo_id": lo_id}).fetchone()
            entity_id = entity_result.entity_id if entity_result else None
            
            self.logger.info(f"🔍 DEBUG: Entity ID: {entity_id}")
            
            # Convert attribute IDs back to proper parameter names for V2 function
            converted_inputs = {}
            for attr_id, value in processed_inputs.items():
                if value is not None:  # Only include non-null values
                    # Get attribute name from entity_attributes table
                    attr_name_query = """
                    SELECT name FROM workflow_runtime.entity_attributes 
                    WHERE attribute_id = :attr_id
                    """
                    attr_result = self.db.execute(text(attr_name_query), {"attr_id": attr_id}).fetchone()
                    if attr_result:
                        param_name = attr_result.name
                        converted_inputs[param_name] = value
                        self.logger.info(f"🔍 DEBUG: Converted {attr_id} -> {param_name} = {value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: No attribute name found for {attr_id}, skipping")
            
            self.logger.info(f"🔍 DEBUG: Converted inputs for V2 function: {converted_inputs}")
            
            # Execute using V2 system functions
            if function_type in ['create', 'update', 'delete']:
                # Use V2 database functions
                from app.services.v2.v2_adapter import execute_system_function_v2
                
                # Add table/entity information for database operations
                v2_inputs = converted_inputs.copy()
                if entity_id:
                    v2_inputs["entity"] = entity_id
                    # Look up actual table name from entities table
                    table_query = "SELECT table_name FROM workflow_runtime.entities WHERE entity_id = :entity_id"
                    table_result = self.db.execute(text(table_query), {"entity_id": entity_id}).fetchone()
                    v2_inputs["table"] = table_result.table_name if table_result else entity_id

                    #v2_inputs["table"] = entity_id  # For V2 functions, entity maps to table
                
                self.logger.info(f"🔧 V2: Executing {function_type}_record with inputs: {v2_inputs}")
                
                result = execute_system_function_v2(
                    function_name=f"{function_type}_record",
                    db_session=self.db,
                    entity_id=entity_id,
                    **v2_inputs
                )
                
                self.logger.info(f"✅ V2: Primary function {function_type}_record executed: {result}")
                return result if isinstance(result, dict) else {"result": result}
            else:
                # For non-CRUD operations, fall back to legacy for now
                self.logger.warning(f"⚠️ Function type {function_type} not yet migrated to V2, using legacy")
                from app.services.function_repository import function_repository
                result = function_repository.auto_execute(function_type, self.db, **converted_inputs)
                return result if isinstance(result, dict) else {"result": result}
            
        except Exception as e:
            self.logger.error(f"Error executing primary function: {str(e)}")
            raise
    
    def _store_execution_outputs(self, instance_id: str, lo_id: str, execution_result: Dict[str, Any]):
        """Store execution outputs in lo_output_execution by copying from input values"""
        try:
            self.logger.info(f"🔍 DEBUG: Storing execution outputs for LO {lo_id}")
            
            # For a create operation, the outputs should be the same as the inputs that were processed
            # Get the processed input values from lo_input_execution
            input_values_query = """
            SELECT lie.input_contextual_id, lie.input_value
            FROM workflow_runtime.lo_input_execution lie
            WHERE lie.instance_id = :instance_id AND lie.lo_id = :lo_id
            AND lie.input_value IS NOT NULL
            """
            
            input_values = self.db.execute(text(input_values_query), {
                "instance_id": instance_id,
                "lo_id": lo_id
            }).fetchall()
            
            self.logger.info(f"🔍 DEBUG: Found {len(input_values)} input values to copy to outputs")
            
            # Update the corresponding output records with the input values
            import json
            for input_row in input_values:
                input_contextual_id = input_row.input_contextual_id
                input_value = input_row.input_value
                
                # Map input contextual ID to output contextual ID (they should match for create operations)
                output_contextual_id = input_contextual_id
                
                # Update the output execution record
                # Ensure proper JSON encoding for JSONB column
                update_query = """
                UPDATE workflow_runtime.lo_output_execution 
                SET output_value = :output_value
                WHERE instance_id = :instance_id 
                AND lo_id = :lo_id 
                AND output_contextual_id = :output_contextual_id
                """
                
                # Ensure the value is properly JSON-encoded for JSONB column
                if input_value is not None:
                    # Check if input_value is already JSON-encoded
                    try:
                        # Try to parse it as JSON first
                        parsed_value = json.loads(input_value)
                        # If successful, it's already JSON-encoded, use as-is
                        final_output_value = input_value
                    except (json.JSONDecodeError, TypeError):
                        # If parsing fails, it's a raw value, encode it
                        final_output_value = json.dumps(input_value)
                else:
                    final_output_value = None
                
                rows_updated = self.db.execute(text(update_query), {
                    "instance_id": instance_id,
                    "lo_id": lo_id,
                    "output_contextual_id": output_contextual_id,
                    "output_value": final_output_value
                }).rowcount
                
                if rows_updated > 0:
                    self.logger.info(f"🔍 DEBUG: Updated output {output_contextual_id} with value from input")
                else:
                    self.logger.warning(f"🔍 DEBUG: No output record found for {output_contextual_id}")
            
            self.logger.info(f"✅ Stored execution outputs for {len(input_values)} fields")
            
        except Exception as e:
            self.logger.error(f"Error storing execution outputs: {str(e)}")
    
    def _process_data_mappings(self, instance_id: str, current_lo_id: str, go_id: str):
        """Process data mappings from current LO to next LOs"""
        try:
            self.logger.info(f"🔍 DEBUG: Processing data mappings for LO {current_lo_id}")
            
            # CRITICAL FIX: Get data mappings where this LO is the TARGET, not the source
            # We need to populate this LO's inputs from previous LO's outputs
            mapping_query = """
            SELECT source, target FROM workflow_runtime.lo_data_mappings
            WHERE target LIKE :lo_pattern
            """
            
            mappings = self.db.execute(text(mapping_query), {
                "lo_pattern": f"%{current_lo_id}%"
            }).fetchall()
            
            self.logger.info(f"🔍 DEBUG: Found {len(mappings)} data mappings for {current_lo_id}")
            
            for mapping in mappings:
                source = mapping.source
                target = mapping.target
                
                self.logger.info(f"🔍 DEBUG: Processing mapping {source} -> {target}")
                
                # Extract target LO ID and contextual ID from target
                # Target format: GO1.LO2.IP1.IT1 -> LO ID: GO1.LO2, Contextual ID: LeaveApplication.leaveId
                target_parts = target.split('.')
                if len(target_parts) >= 3:
                    target_lo_id = f"{target_parts[0]}.{target_parts[1]}"  # GO1.LO2
                    
                    # Get the contextual_id (slot_id) for this target item
                    contextual_query = """
                    SELECT slot_id FROM workflow_runtime.lo_input_items 
                    WHERE item_id = :target_item_id
                    """
                    contextual_result = self.db.execute(text(contextual_query), {
                        "target_item_id": target
                    }).fetchone()
                    
                    if contextual_result:
                        target_contextual_id = contextual_result.slot_id
                        
                        # UPDATE existing input execution record (records are pre-created during workflow instance creation)
                        # First get the source slot_id to avoid subquery cardinality issues
                        source_slot_query = """
                        SELECT slot_id FROM workflow_runtime.lo_output_items 
                        WHERE item_id = :source_item_id
                        LIMIT 1
                        """
                        source_slot_result = self.db.execute(text(source_slot_query), {
                            "source_item_id": source
                        }).fetchone()
                        
                        if source_slot_result:
                            source_slot_id = source_slot_result.slot_id
                            
                            # CRITICAL FIX: Extract source LO ID from the source item_id
                            # Source format: GO1.LO1.OP.IT1 -> source_lo_id = GO1.LO1
                            source_parts = source.split('.')
                            if len(source_parts) >= 3:
                                source_lo_id = f"{source_parts[0]}.{source_parts[1]}"  # GO1.LO1
                            else:
                                source_lo_id = current_lo_id  # fallback
                            
                            update_query = """
                            UPDATE workflow_runtime.lo_input_execution 
                            SET input_value = (
                                SELECT output_value 
                                FROM workflow_runtime.lo_output_execution
                                WHERE instance_id = :instance_id 
                                AND output_contextual_id = :source_slot_id
                                AND lo_id = :source_lo_id
                            ),
                            updated_at = NOW(),
                            updated_by = 'data_mapping'
                            WHERE instance_id = :instance_id 
                            AND lo_id = :target_lo_id
                            AND input_contextual_id = :target_contextual_id
                            """
                            
                            rows_updated = self.db.execute(text(update_query), {
                                "instance_id": instance_id,
                                "source_slot_id": source_slot_id,
                                "source_lo_id": source_lo_id,
                                "target_lo_id": target_lo_id,
                                "target_contextual_id": target_contextual_id
                            }).rowcount
                        else:
                            self.logger.warning(f"🔍 DEBUG: No source slot_id found for {source}")
                            rows_updated = 0
                        

                        
                        self.logger.info(f"🔍 DEBUG: Updated {rows_updated} records for mapping {source} -> {target}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: No contextual_id found for target {target}")
                else:
                    self.logger.warning(f"🔍 DEBUG: Invalid target format: {target}")
            
        except Exception as e:
            self.logger.error(f"Error processing data mappings: {str(e)}")
    
    def _resolve_next_local_objective(self, current_lo_id: str, processed_inputs: Dict[str, Any]) -> Optional[str]:
        """Determine next LO based on execution pathways"""
        try:
            self.logger.info(f"🔍 DEBUG: Resolving next LO for {current_lo_id}")
            self.logger.info(f"🔍 DEBUG: Processed inputs: {processed_inputs}")
            
            # Check for terminal pathway
            terminal_query = """
            SELECT 1 FROM workflow_runtime.execution_pathways
            WHERE lo_id = :lo_id AND LOWER(pathway_type) = 'terminal'
            """
            
            is_terminal = self.db.execute(text(terminal_query), {"lo_id": current_lo_id}).fetchone()
            
            if is_terminal:
                self.logger.info(f"🔍 DEBUG: {current_lo_id} is terminal pathway")
                return None
            
            # Check for conditional pathways
            condition_query = """
            SELECT condition_entity, condition_attribute, condition_operator, condition_value, next_lo
            FROM workflow_runtime.execution_pathway_conditions
            WHERE lo_id = :lo_id
            ORDER BY id ASC
            """
            
            conditions = self.db.execute(text(condition_query), {"lo_id": current_lo_id}).fetchall()
            self.logger.info(f"🔍 DEBUG: Found {len(conditions)} pathway conditions")
            
            for condition in conditions:
                self.logger.info(f"🔍 DEBUG: Evaluating condition: {condition.condition_attribute} {condition.condition_operator} {condition.condition_value} -> {condition.next_lo}")
                
                # CRITICAL FIX: Map condition attribute to attribute ID
                attr_value = None
                
                # First try direct lookup (for backward compatibility)
                if condition.condition_attribute in processed_inputs:
                    attr_value = processed_inputs[condition.condition_attribute]
                    self.logger.info(f"🔍 DEBUG: Found direct attribute value: {condition.condition_attribute} = {attr_value}")
                else:
                    # Map condition attribute name to attribute ID
                    # Extract attribute name from condition_attribute (e.g., "LeaveApplication.requiresDocumentation" -> "requiresDocumentation")
                    if "." in condition.condition_attribute:
                        attr_name = condition.condition_attribute.split(".")[-1]
                    else:
                        attr_name = condition.condition_attribute
                    
                    self.logger.info(f"🔍 DEBUG: Looking for attribute name: {attr_name}")
                    
                    # Get attribute ID for this attribute name
                    attr_id_query = """
                    SELECT attribute_id FROM workflow_runtime.entity_attributes 
                    WHERE name = :attr_name
                    """
                    attr_id_result = self.db.execute(text(attr_id_query), {"attr_name": attr_name}).fetchone()
                    
                    if attr_id_result:
                        attr_id = attr_id_result.attribute_id
                        attr_value = processed_inputs.get(attr_id)
                        self.logger.info(f"🔍 DEBUG: Mapped {attr_name} -> {attr_id}, value: {attr_value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: No attribute ID found for {attr_name}")
                
                if attr_value is not None:
                    condition_result = self._evaluate_condition(attr_value, condition.condition_operator, condition.condition_value)
                    self.logger.info(f"🔍 DEBUG: Condition evaluation: {attr_value} {condition.condition_operator} {condition.condition_value} = {condition_result}")
                    
                    if condition_result:
                        self.logger.info(f"🔍 DEBUG: Condition matched! Next LO: {condition.next_lo}")
                        return condition.next_lo
                else:
                    self.logger.warning(f"🔍 DEBUG: No value found for condition attribute {condition.condition_attribute}")
            
            # Fall back to sequential pathway
            self.logger.info(f"🔍 DEBUG: No conditional pathways matched, checking sequential pathway")
            sequential_query = """
            SELECT next_lo FROM workflow_runtime.execution_pathways
            WHERE lo_id = :lo_id AND LOWER(pathway_type) = 'sequential'
            LIMIT 1
            """
            
            sequential_result = self.db.execute(text(sequential_query), {"lo_id": current_lo_id}).fetchone()
            
            if sequential_result:
                self.logger.info(f"🔍 DEBUG: Sequential pathway found: {sequential_result.next_lo}")
                return sequential_result.next_lo
            else:
                self.logger.warning(f"🔍 DEBUG: No sequential pathway found for {current_lo_id}")
                return None
            
        except Exception as e:
            self.logger.error(f"Error resolving next LO: {str(e)}")
            return None
    
    def _evaluate_condition(self, actual_value: Any, operator: str, expected_value: str) -> bool:
        """Evaluate pathway condition"""
        try:
            # CRITICAL FIX: Handle boolean values properly
            if isinstance(actual_value, bool) and expected_value.lower() in ['true', 'false']:
                expected_bool = expected_value.lower() == 'true'
                if operator.lower() == "equals":
                    return actual_value == expected_bool
                elif operator.lower() == "not_equals":
                    return actual_value != expected_bool
            
            # For non-boolean values, use string comparison
            actual_str = str(actual_value).lower() if actual_value is not None else ""
            expected_str = str(expected_value).lower()
            
            if operator.lower() == "equals":
                return actual_str == expected_str
            elif operator.lower() == "not_equals":
                return actual_str != expected_str
            elif operator.lower() == "contains":
                return expected_str in actual_str
            elif operator.lower() == "in":
                return actual_str in [v.strip().lower() for v in expected_str.split(",")]
            else:
                return False
                
        except Exception:
            return False
    
    def _update_workflow_instance(self, instance_id: str, next_lo_id: Optional[str]):
        """Update workflow instance with next LO or completion status and auto-execute SYSTEM LOs"""
        try:
            if next_lo_id:
                # Move to next LO
                update_query = """
                UPDATE workflow_runtime.workflow_instances
                SET current_lo_id = :next_lo_id, updated_at = NOW()
                WHERE instance_id = :instance_id
                """
                
                self.db.execute(text(update_query), {
                    "next_lo_id": next_lo_id,
                    "instance_id": instance_id
                })
                
                # CRITICAL: Check if next LO is SYSTEM type and auto-execute it
                self._check_and_auto_execute_system_lo(instance_id, next_lo_id)
                
            else:
                # Complete workflow
                complete_query = """
                UPDATE workflow_runtime.workflow_instances
                SET status = 'Completed', current_lo_id = NULL, completed_at = NOW(), updated_at = NOW()
                WHERE instance_id = :instance_id
                """
                
                self.db.execute(text(complete_query), {"instance_id": instance_id})
            
        except Exception as e:
            self.logger.error(f"Error updating workflow instance: {str(e)}")
    
    def _check_and_auto_execute_system_lo(self, instance_id: str, lo_id: str):
        """Check if LO is SYSTEM type and auto-execute it"""
        try:
            self.logger.info(f"🔍 Checking if {lo_id} is SYSTEM type for auto-execution...")
            
            # Check if this LO is SYSTEM agent type
            system_check_query = """
            SELECT function_type, name, agent_type FROM workflow_runtime.local_objectives 
            WHERE lo_id = :lo_id
            """
            
            result = self.db.execute(text(system_check_query), {"lo_id": lo_id}).fetchone()
            
            if not result:
                self.logger.warning(f"LO {lo_id} not found for system check")
                return
            
            function_type = result.function_type
            lo_name = result.name
            agent_type = result.agent_type
            
            self.logger.info(f"🔍 LO {lo_id} ({lo_name}) has function_type: {function_type}, agent_type: {agent_type}")
            
            if agent_type and agent_type.upper() == 'SYSTEM':
                self.logger.info(f"🤖 AUTO-EXECUTING SYSTEM AGENT LO: {lo_id} ({lo_name})")
                
                # Get workflow instance details for auto-execution
                instance_query = """
                SELECT tenant_id, started_by FROM workflow_runtime.workflow_instances 
                WHERE instance_id = :instance_id
                """
                
                instance_result = self.db.execute(text(instance_query), {"instance_id": instance_id}).fetchone()
                
                if not instance_result:
                    self.logger.error(f"Cannot find workflow instance {instance_id} for auto-execution")
                    return
                
                tenant_id = instance_result.tenant_id
                user_id = instance_result.started_by  # Use original workflow starter
                
                # Auto-execute the SYSTEM LO with empty input data (system will populate)
                self.logger.info(f"🤖 Executing SYSTEM LO {lo_id} with user {user_id}, tenant {tenant_id}")
                
                auto_execution_result = self.execute_local_objective(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    instance_id=instance_id,
                    input_data={}  # SYSTEM LOs don't need user input
                )
                
                if auto_execution_result:
                    self.logger.info(f"✅ SYSTEM AGENT LO {lo_id} auto-executed successfully: {auto_execution_result.get('status')}")
                    
                    # Check if there's another LO to process (chain execution)
                    next_lo = auto_execution_result.get('next_lo_id')
                    if next_lo:
                        self.logger.info(f"🔄 SYSTEM LO {lo_id} completed, checking next LO: {next_lo}")
                        # Recursively check if the next LO is also SYSTEM type
                        self._check_and_auto_execute_system_lo(instance_id, next_lo)
                else:
                    self.logger.error(f"❌ SYSTEM AGENT LO {lo_id} auto-execution failed")
            else:
                self.logger.info(f"📋 LO {lo_id} is not SYSTEM agent type (agent_type: {agent_type}), no auto-execution needed")
                
        except Exception as e:
            self.logger.error(f"Error in auto-execution check for {lo_id}: {str(e)}")
    
    def _create_lo_execution_record(
        self, 
        instance_id: str, 
        lo_id: str, 
        go_id: str,
        user_id: str, 
        inputs: Dict[str, Any], 
        outputs: Dict[str, Any]
    ) -> str:
        """Create individual LO execution record for tracking with separate columns"""
        try:
            # Get table_name from local_objectives table
            table_name_query = """
            SELECT table_name FROM workflow_runtime.local_objectives WHERE lo_id = :lo_id
            """
            table_name_result = self.db.execute(text(table_name_query), {"lo_id": lo_id}).fetchone()
            
            if table_name_result and table_name_result.table_name:
                table_name = table_name_result.table_name
                self.logger.info(f"Using stored table_name from database: {table_name}")
            else:
                # Fallback to old logic if table_name is not set
                lo_name_query = """
                SELECT name FROM workflow_runtime.local_objectives WHERE lo_id = :lo_id
                """
                lo_name_result = self.db.execute(text(lo_name_query), {"lo_id": lo_id}).fetchone()
                lo_name = lo_name_result.name if lo_name_result else lo_id
                
                # Create table name: Since lo_id already contains GO (e.g., GO1.LO1), just use lo_id + name
                # Convert GO1.LO1 to GO1_LO1 and append name
                clean_lo_id = lo_id.replace(".", "_")
                clean_lo_name = lo_name.replace(" ", "").replace("-", "_").replace(".", "_")
                table_name = f"{clean_lo_id}_{clean_lo_name}"
                self.logger.warning(f"No table_name found in database, using generated: {table_name}")
            
            # Create table with separate columns for each input/output
            self._ensure_lo_execution_table_exists_with_columns(table_name, lo_id, inputs, outputs)
            
            # Insert execution record with separate column values
            self._insert_lo_execution_record(table_name, instance_id, user_id, inputs, outputs)
            
            return table_name
            
        except Exception as e:
            self.logger.error(f"Error creating LO execution record: {str(e)}")
            return f"{go_id}_{lo_id}_execution"
    
    def _ensure_lo_execution_table_exists_with_columns(self, table_name: str, lo_id: str, inputs: Dict[str, Any], outputs: Dict[str, Any]):
        """Create individual LO execution table with separate columns for each input/output"""
        try:
            # Get input field names from lo_input_items
            input_fields_query = """
            SELECT lii.slot_id, ea.name as attribute_name, ea.datatype as data_type
            FROM workflow_runtime.lo_input_items lii
            JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
            WHERE lii.lo_id = :lo_id
            ORDER BY lii.id
            """
            
            input_fields = self.db.execute(text(input_fields_query), {"lo_id": lo_id}).fetchall()
            
            # Get output field names from lo_output_items
            output_fields_query = """
            SELECT loo.slot_id, ea.name as attribute_name, ea.datatype as data_type
            FROM workflow_runtime.lo_output_items loo
            JOIN workflow_runtime.entity_attributes ea ON loo.attribute_id = ea.attribute_id
            WHERE loo.lo_id = :lo_id
            ORDER BY loo.id
            """
            
            output_fields = self.db.execute(text(output_fields_query), {"lo_id": lo_id}).fetchall()
            
            # Build column definitions
            columns = []
            columns.append("id SERIAL PRIMARY KEY")
            columns.append("workflow_instance_id UUID NOT NULL")
            columns.append("user_id VARCHAR(255) NOT NULL")
            columns.append("execution_timestamp TIMESTAMP NOT NULL")
            columns.append("status VARCHAR(50) NOT NULL")
            columns.append("created_at TIMESTAMP DEFAULT NOW()")
            
            # Add input columns
            for field in input_fields:
                if field.attribute_name:
                    col_name = f"input_{field.attribute_name.lower()}"
                    col_type = self._map_data_type_to_sql(field.data_type)
                    columns.append(f"{col_name} {col_type}")
            
            # Add output columns
            for field in output_fields:
                if field.attribute_name:
                    col_name = f"output_{field.attribute_name.lower()}"
                    col_type = self._map_data_type_to_sql(field.data_type)
                    columns.append(f"{col_name} {col_type}")
            
            # Create table
            create_table_query = f"""
            CREATE TABLE IF NOT EXISTS workflow_runtime.{table_name} (
                {', '.join(columns)}
            )
            """
            
            self.db.execute(text(create_table_query))
            self.logger.info(f"Created LO execution table {table_name} with {len(columns)} columns")
            
        except Exception as e:
            self.logger.error(f"Error creating LO execution table {table_name}: {str(e)}")
    
    def _map_data_type_to_sql(self, data_type: str) -> str:
        """Map entity attribute data types to SQL column types"""
        if not data_type:
            return "TEXT"
        
        data_type_lower = data_type.lower()
        
        if data_type_lower in ['string', 'text', 'varchar']:
            return "TEXT"
        elif data_type_lower in ['integer', 'int']:
            return "INTEGER"
        elif data_type_lower in ['boolean', 'bool']:
            return "BOOLEAN"
        elif data_type_lower in ['date']:
            return "DATE"
        elif data_type_lower in ['datetime', 'timestamp']:
            return "TIMESTAMP"
        elif data_type_lower in ['decimal', 'numeric', 'float']:
            return "DECIMAL"
        else:
            return "TEXT"  # Default fallback
    
    def _insert_lo_execution_record(self, table_name: str, instance_id: str, user_id: str, inputs: Dict[str, Any], outputs: Dict[str, Any]):
        """Insert execution record with separate column values"""
        try:
            self.logger.info(f"🔍 DEBUG: _insert_lo_execution_record called with:")
            self.logger.info(f"🔍 DEBUG: table_name: {table_name}")
            self.logger.info(f"🔍 DEBUG: instance_id: {instance_id}")
            self.logger.info(f"🔍 DEBUG: inputs: {inputs}")
            self.logger.info(f"🔍 DEBUG: outputs: {outputs}")
            
            # Build column names and values
            columns = ['workflow_instance_id', 'user_id', 'execution_timestamp', 'status', 'created_at']
            values = [':instance_id', ':user_id', 'NOW()', "'completed'", 'NOW()']
            params = {'instance_id': instance_id, 'user_id': user_id}
            
            # Convert attribute IDs to attribute names for inputs
            for attr_id, value in inputs.items():
                if value is not None:
                    # Get attribute name from entity_attributes table
                    attr_name_query = """
                    SELECT name FROM workflow_runtime.entity_attributes 
                    WHERE attribute_id = :attr_id
                    """
                    attr_result = self.db.execute(text(attr_name_query), {"attr_id": attr_id}).fetchone()
                    if attr_result:
                        col_name = f"input_{attr_result.name.lower()}"
                        columns.append(col_name)
                        param_name = f"input_{attr_result.name.lower()}"
                        values.append(f":{param_name}")
                        
                        # CRITICAL FIX: JSON serialize complex data types
                        serialized_value = self._serialize_for_lo_execution(value)
                        params[param_name] = serialized_value
            
            # Get actual output values from lo_output_execution table for THIS SPECIFIC LO
            output_values_query = """
            SELECT loe.output_contextual_id, loe.output_value, ea.name as attribute_name
            FROM workflow_runtime.lo_output_execution loe
            JOIN workflow_runtime.lo_output_items loo ON loe.output_contextual_id = loo.slot_id AND loo.lo_id = loe.lo_id
            JOIN workflow_runtime.entity_attributes ea ON loo.attribute_id = ea.attribute_id
            WHERE loe.instance_id = :instance_id AND loe.lo_id = :lo_id AND loe.output_value IS NOT NULL
            """
            
            # Get the LO ID from the table name (extract from GO1_LO3_ReviewLeaveRequest -> GO1.LO3)
            lo_id_parts = table_name.split('_')
            if len(lo_id_parts) >= 2:
                lo_id = f"{lo_id_parts[0]}.{lo_id_parts[1]}"
            else:
                lo_id = 'GO1.LO3'  # fallback
            
            output_results = self.db.execute(text(output_values_query), {
                "instance_id": instance_id,
                "lo_id": lo_id
            }).fetchall()
            
            # Add output columns with actual values from lo_output_execution
            # CRITICAL FIX: Use correct column naming format op_{entity}_{attribute}
            for output_row in output_results:
                if output_row.attribute_name and output_row.output_value:
                    # Get entity name for this output to match database schema
                    # Database columns are in format: op_leaveapplication_employeeid
                    entity_name_query = """
                    SELECT e.name FROM workflow_runtime.entities e
                    JOIN workflow_runtime.lo_output_items loo ON loo.entity_id = e.entity_id
                    WHERE loo.attribute_id = (
                        SELECT attribute_id FROM workflow_runtime.entity_attributes 
                        WHERE name = :attribute_name LIMIT 1
                    ) AND loo.lo_id = :lo_id
                    LIMIT 1
                    """
                    
                    entity_result = self.db.execute(text(entity_name_query), {
                        "attribute_name": output_row.attribute_name,
                        "lo_id": lo_id
                    }).fetchone()
                    
                    if entity_result:
                        entity_name = entity_result.name.lower()
                        # CRITICAL FIX: Use correct format to match existing table schema: output_{attribute}
                        col_name = f"output_{output_row.attribute_name.lower()}"
                        columns.append(col_name)
                        param_name = f"output_{output_row.attribute_name.lower()}"
                        values.append(f":{param_name}")
                        
                        # Parse JSON value if needed
                        import json
                        try:
                            parsed_value = json.loads(output_row.output_value)
                        except (json.JSONDecodeError, TypeError):
                            parsed_value = output_row.output_value
                        
                        # CRITICAL FIX: JSON serialize complex data types for output values too
                        serialized_output_value = self._serialize_for_lo_execution(parsed_value)
                        params[param_name] = serialized_output_value
                        
                        self.logger.info(f"🔍 DEBUG: Added output column {col_name} with value {serialized_output_value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: Could not find entity name for attribute {output_row.attribute_name}")
            
            # Insert record
            insert_query = f"""
            INSERT INTO workflow_runtime.{table_name} ({', '.join(columns)})
            VALUES ({', '.join(values)})
            """
            
            self.db.execute(text(insert_query), params)
            self.logger.info(f"Inserted execution record into {table_name} with {len(columns)} columns")
            
        except Exception as e:
            self.logger.error(f"Error inserting LO execution record into {table_name}: {str(e)}")
    
    def _update_workflow_transaction(
        self, 
        instance_id: str, 
        go_id: str, 
        lo_id: str, 
        tenant_id: str, 
        user_id: str, 
        inputs: Dict[str, Any], 
        outputs: Dict[str, Any]
    ):
        """Update workflow transaction table with proper datetime serialization"""
        try:
            import json
            from datetime import datetime, date
            from decimal import Decimal
            
            # Custom JSON serializer for datetime/date/decimal objects
            def json_serializer(obj):
                if isinstance(obj, (datetime, date)):
                    return obj.isoformat()
                elif isinstance(obj, Decimal):
                    return float(obj)
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            insert_query = """
            INSERT INTO workflow_runtime.workflow_transaction (
                workflow_instance_id, go_id, lo_id, tenant_id, user_id, 
                status, input_stack, output_stack, created_at, updated_at
            ) VALUES (
                :instance_id, :go_id, :lo_id, :tenant_id, :user_id,
                'completed', :input_stack, :output_stack, NOW(), NOW()
            )
            """
            
            self.db.execute(text(insert_query), {
                "instance_id": instance_id,
                "go_id": go_id,
                "lo_id": lo_id,
                "tenant_id": tenant_id,
                "user_id": user_id,
                "input_stack": json.dumps(inputs, default=json_serializer),
                "output_stack": json.dumps(outputs, default=json_serializer)
            })
            
        except Exception as e:
            self.logger.error(f"Error updating workflow transaction: {str(e)}")
    
    def _extract_field_value_from_result(self, result: Any, slot_id: str, output_contextual_id: str) -> Any:
        """
        Extract specific field value from nested function result based on slot_id and output context.
        This handles fetch_by_id results that return lists of dictionaries and extracts simple values.
        CRITICAL: Returns simple string/primitive values, not complex data structures.
        """
        try:
            self.logger.info(f"🔍 DEBUG: Extracting field value for slot_id: {slot_id}, output_id: {output_contextual_id}")
            self.logger.info(f"🔍 DEBUG: Result type: {type(result)}, Result: {result}")
            
            # If result is None, return None
            if result is None:
                return None
            
            # If result is a list (typical for fetch_by_id), extract from first record
            if isinstance(result, list) and len(result) > 0:
                record = result[0]  # Take first record
                if isinstance(record, dict):
                    # Extract field based on slot_id pattern
                    # slot_id format: "Employee.email" -> extract "email" field
                    if "." in slot_id:
                        field_name = slot_id.split(".")[-1]  # Get last part after dot
                    else:
                        field_name = slot_id
                    
                    # Try exact field name first
                    if field_name in record:
                        field_value = record[field_name]
                        # CRITICAL: Return the actual field value (already simple)
                        self.logger.info(f"🔍 DEBUG: Extracted field {field_name} = {field_value}")
                        return field_value
                    
                    # Try lowercase version
                    field_name_lower = field_name.lower()
                    if field_name_lower in record:
                        field_value = record[field_name_lower]
                        # CRITICAL: Return the actual field value (already simple)
                        self.logger.info(f"🔍 DEBUG: Extracted field (lowercase) {field_name_lower} = {field_value}")
                        return field_value
                    
                    # Try common variations
                    for key in record.keys():
                        if key.lower() == field_name_lower:
                            field_value = record[key]
                            # CRITICAL: Return the actual field value (already simple)
                            self.logger.info(f"🔍 DEBUG: Extracted field (case-insensitive) {key} = {field_value}")
                            return field_value
                    
                    self.logger.warning(f"🔍 DEBUG: Field {field_name} not found in record. Available fields: {list(record.keys())}")
                    return None
                else:
                    # If record is not a dict, return the record itself
                    self.logger.info(f"🔍 DEBUG: Record is not a dict, returning as-is: {record}")
                    return record
            
            # If result is a single dict, extract field directly
            elif isinstance(result, dict):
                # Extract field based on slot_id pattern
                if "." in slot_id:
                    field_name = slot_id.split(".")[-1]  # Get last part after dot
                else:
                    field_name = slot_id
                
                # Try exact field name first
                if field_name in result:
                    field_value = result[field_name]
                    # CRITICAL: Return the actual field value (already simple)
                    self.logger.info(f"🔍 DEBUG: Extracted field {field_name} = {field_value}")
                    return field_value
                
                # Try lowercase version
                field_name_lower = field_name.lower()
                if field_name_lower in result:
                    field_value = result[field_name_lower]
                    # CRITICAL: Return the actual field value (already simple)
                    self.logger.info(f"🔍 DEBUG: Extracted field (lowercase) {field_name_lower} = {field_value}")
                    return field_value
                
                # Try common variations
                for key in result.keys():
                    if key.lower() == field_name_lower:
                        field_value = result[key]
                        # CRITICAL: Return the actual field value (already simple)
                        self.logger.info(f"🔍 DEBUG: Extracted field (case-insensitive) {key} = {field_value}")
                        return field_value
                
                self.logger.warning(f"🔍 DEBUG: Field {field_name} not found in result. Available fields: {list(result.keys())}")
                return None
            
            # For other types, return as-is
            else:
                self.logger.info(f"🔍 DEBUG: Result is not list or dict, returning as-is: {result}")
                return result
                
        except Exception as e:
            self.logger.error(f"Error extracting field value from result: {str(e)}")
            return None
    
    def _serialize_for_lo_execution(self, value: Any) -> Any:
        """Serialize complex data types for LO execution table insertion"""
        import json
        from datetime import datetime, date
        from decimal import Decimal
        
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            # Simple types - pass through
            return value
        elif isinstance(value, (list, dict)):
            # Complex types - JSON serialize
            try:
                # Handle nested datetime/date/decimal objects
                def json_serializer(obj):
                    if isinstance(obj, (datetime, date)):
                        return obj.isoformat()
                    elif isinstance(obj, Decimal):
                        return float(obj)
                    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
                
                serialized = json.dumps(value, default=json_serializer)
                self.logger.info(f"🔍 DEBUG: JSON serialized complex value for LO execution: {type(value)} -> {serialized[:100]}...")
                return serialized
            except Exception as e:
                self.logger.error(f"Failed to JSON serialize value for LO execution {type(value)}: {str(e)}")
                # Fallback to string representation
                return str(value)
        elif isinstance(value, (datetime, date)):
            # Date/datetime objects - convert to ISO format
            return value.isoformat()
        elif isinstance(value, Decimal):
            # Decimal objects - convert to float
            return float(value)
        else:
            # Other types - convert to string
            self.logger.info(f"🔍 DEBUG: Converting {type(value)} to string for LO execution table")
            return str(value)


class LocalObjectivesService:
    """Service class for local objectives operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self.rbac = RBACService(db_session)
        self.nested_function_service = NestedFunctionService(db_session)
        self.dropdown_service = DropdownService(db_session)
    
    def get_workflow_instance_inputs(
        self, 
        user_id: str, 
        tenant_id: str,
        instance_id: str
    ) -> Optional[LocalObjectiveInputsResponse]:
        """Get inputs for a workflow instance with RBAC checks."""
        try:
            self.logger.info(f"Getting inputs for workflow instance: {instance_id}")
            
            # Get workflow instance and current LO first
            instance_query = """
            SELECT wi.instance_id, wi.go_id, wi.current_lo_id, wi.tenant_id
            FROM workflow_runtime.workflow_instances wi
            WHERE wi.instance_id = :instance_id AND wi.tenant_id = :tenant_id
            """
            
            instance_result = self.db.execute(text(instance_query), {
                "instance_id": instance_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            if not instance_result:
                self.logger.warning(f"Workflow instance {instance_id} not found")
                return None
            
            current_lo_id = instance_result.current_lo_id
            
            if not current_lo_id:
                self.logger.warning(f"No current LO found for instance {instance_id}")
                return None
            
            # Check RBAC permissions for the specific LO (not generic workflow_instances)
            if not self.rbac.check_user_permissions(user_id, tenant_id, current_lo_id, "read"):
                self.logger.warning(f"User {user_id} denied read access to LO {current_lo_id}")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            # Get all input items for the current LO
            return self._get_lo_inputs(current_lo_id, instance_id)
            
        except Exception as e:
            self.logger.error(f"Error getting workflow instance inputs: {str(e)}")
            return None
    
    def _get_lo_ui_form(self, lo_id: str) -> Optional[Dict[str, Any]]:
        """Get LO UI form information."""
        try:
            query = """
            SELECT lus.stack_id, lus.description, lus.ui_type
            FROM workflow_runtime.lo_ui_stacks lus
            WHERE lus.lo_id = :lo_id
            LIMIT 1
            """
            
            result = self.db.execute(text(query), {"lo_id": lo_id}).fetchone()
            
            if result:
                return {
                    "ui_stack_id": result.stack_id,
                    "description": result.description,
                    "ui_type": result.ui_type
                }
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting LO UI form: {str(e)}")
            return None
    
    def _get_lo_inputs(self, lo_id: str, instance_id: Optional[str] = None) -> LocalObjectiveInputsResponse:
        """Get all input items for a local objective."""
        try:
            self.logger.info(f"Getting inputs for LO: {lo_id}")
            
            # Get LO UI form information
            ui_form = self._get_lo_ui_form(lo_id)
            
            
            # Get all input items for the LO - FIXED QUERY WITH CONTEXTUAL_ID
            query = """
            SELECT 
                lii.id, lii.item_id, lii.input_stack_id, lii.slot_id, lii.source_type,
                lii.source_description, lii.required, lii.lo_id, lii.data_type, lii.ui_control,
                lii.nested_function_id, lii.is_visible, lii.name, lii.type, lii.read_only,
                lii.agent_type, lii.dependent_attribute, lii.dependent_attribute_value,
                lii.enum_values, lii.default_value, lii.information_field, lii.constant_field,
                lii.entity_id, lii.attribute_id, lii.entity_name, lii.attribute_name,
                ea.display_name
            FROM workflow_runtime.lo_input_items lii
            LEFT JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
            WHERE lii.lo_id = :lo_id
            ORDER BY lii.id
            """
            
            
            result = self.db.execute(text(query), {"lo_id": lo_id}).fetchall()
            
            # Categorize inputs by corrected logic
            user_inputs = []
            system_inputs = []
            info_inputs = []
            dependent_inputs = []
            dependencies = {}
            
            for row in result:
                try:
                    input_field = self._build_input_field_response(row, instance_id)
                    
                    self.logger.info(f"DEBUG: Processing field {input_field.item_id} - source_type: {input_field.source_type}, nested_function_id: {input_field.nested_function_id}")
                except Exception as e:
                    self.logger.error(f"DEBUG: Error processing row {getattr(row, 'item_id', 'unknown')}: {str(e)}")
                    continue
                
                # Handle different source types based on your specification
                if input_field.source_type == "user":
                    # User inputs - get from user
                    user_inputs.append(input_field)
                    
                elif input_field.source_type == "nested_function":
                    # Nested function - run nested system function
                    self.logger.info(f"DEBUG: About to execute nested function for {input_field.item_id} with nested_function_id: {input_field.nested_function_id}")
                    system_value = self._execute_system_function(input_field.nested_function_id)
                    input_field.input_value = system_value
                    system_inputs.append(input_field)
                    
                elif input_field.source_type == "mapping":
                    # Mapping - fetch from input execution table
                    if instance_id and input_field.item_id:
                        try:
                            mapped_value = self._get_input_value(instance_id, input_field.item_id)
                            input_field.input_value = mapped_value
                            system_inputs.append(input_field)
                        except Exception as e:
                            self.logger.error(f"Error getting mapped value for {input_field.item_id}: {str(e)}")
                            input_field.input_value = None
                            system_inputs.append(input_field)
                    else:
                        system_inputs.append(input_field)
                        
                elif input_field.source_type == "system_dependent":
                    # System dependent - check if dependent values provided, then execute nested function
                    # For now, add to dependent inputs (will be resolved when parent values provided)
                    
                    # Add dependent_attribute_ids mapping for UI binding
                    if input_field.dependent_attribute_value:
                        dependent_attribute_ids = {}
                        # Parse comma-separated dependent attributes
                        dependent_attrs = [attr.strip() for attr in input_field.dependent_attribute_value.split(',')]
                        
                        for attr_name in dependent_attrs:
                            # Find the item_id for this attribute name
                            attr_item_id = self._find_item_id_by_attribute_name(attr_name, lo_id)
                            if attr_item_id:
                                dependent_attribute_ids[attr_name] = attr_item_id
                                self.logger.info(f"Mapped dependent attribute {attr_name} -> {attr_item_id}")
                        
                        input_field.dependent_attribute_ids = dependent_attribute_ids
                    
                    dependent_inputs.append(input_field)
                    if input_field.dependent_attribute_value:
                        parent_field = input_field.dependent_attribute_value
                        if parent_field not in dependencies:
                            dependencies[parent_field] = []
                        dependencies[parent_field].append(input_field.item_id)
                        
                elif input_field.source_type == "constant":
                    # Constant - get values from constant table
                    constant_value = self._get_constant_value(input_field.entity_id, input_field.attribute_id)
                    input_field.input_value = constant_value
                    system_inputs.append(input_field)
                    
                elif input_field.source_type == "information":
                    # Information - get value from entity.attribute and surface it
                    if input_field.nested_function_id:
                        info_value = self._execute_system_function(input_field.nested_function_id)
                        input_field.input_value = info_value
                    info_inputs.append(input_field)
                    
                elif input_field.source_type == "api":
                    # API - call API endpoint (placeholder for future implementation)
                    self.logger.info(f"API source type for {input_field.item_id} - not implemented yet")
                    input_field.input_value = None
                    system_inputs.append(input_field)
                    
                else:
                    # Unknown source type - default to user input
                    self.logger.warning(f"Unknown source type '{input_field.source_type}' for {input_field.item_id}, defaulting to user input")
                    user_inputs.append(input_field)
                
                # Get dropdown options for user inputs (only for non-dependent fields)
                if input_field.source_type == "user" and not input_field.dependent_attribute:
                    dropdown_options = self.dropdown_service.get_dropdown_options(input_field.item_id)
                    if dropdown_options:
                        input_field.has_dropdown_source = True
                        input_field.dropdown_options = dropdown_options
            
            # Build response with UI form information
            response = LocalObjectiveInputsResponse(
                local_objective=lo_id,
                user_inputs=user_inputs,
                system_inputs=system_inputs,
                info_inputs=info_inputs,
                dependent_inputs=dependent_inputs,
                dependencies=dependencies
            )
            
            # Add UI form information if available
            if ui_form:
                response.lo_ui_stack = ui_form
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error getting LO inputs: {str(e)}")
            raise
    
    def _build_input_field_response(self, row, instance_id: Optional[str] = None) -> InputFieldResponse:
        """Build InputFieldResponse from database row."""
        # Get default UI control if not set
        ui_control = row.ui_control
        
        return InputFieldResponse(
            id=row.id,
            item_id=row.item_id,
            input_stack_id=row.input_stack_id,
            slot_id=row.slot_id,
            source_type=row.source_type,
            source_description=row.source_description,
            required=row.required,
            lo_id=row.lo_id,
            data_type=row.data_type,
            ui_control=ui_control,  # Use computed UI control
            nested_function_id=row.nested_function_id,
            is_visible=row.is_visible,
            name=row.name,
            type=row.type,
            read_only=row.read_only,
            agent_type=row.agent_type,
            dependent_attribute=row.dependent_attribute,
            dependent_attribute_value=row.dependent_attribute_value,
            enum_values=row.enum_values,
            default_value=row.default_value,
            information_field=row.information_field,
            constant_field=row.constant_field,
            entity_id=row.entity_id,
            attribute_id=row.attribute_id,
            entity_name=row.entity_name,
            attribute_name=row.attribute_name,
            display_name=row.display_name or row.attribute_name or row.name,
            contextual_id=row.item_id
        )
    
    def _get_input_value(self, instance_id: str, contextual_id: str) -> Any:
        """Get current input value for an instance and contextual ID."""
        try:
            query = """
            SELECT lie.input_value
            FROM workflow_runtime.lo_input_execution lie
            WHERE lie.instance_id = :instance_id AND lie.input_contextual_id = :contextual_id
            ORDER BY lie.created_at DESC
            LIMIT 1
            """
            
            result = self.db.execute(text(query), {
                "instance_id": instance_id,
                "contextual_id": contextual_id

            }).fetchone()
            
            return result.input_value if result else None
            
        except Exception as e:
            self.logger.error(f"Error getting input value for {contextual_id}: {str(e)}")
            # Rollback the transaction to prevent it from being aborted
            self.db.rollback()
            return None
    
    def _get_constant_value(self, entity_id: str, attribute_id: str) -> Any:
        """Get constant value from constants table."""
        try:
            query = """
            SELECT c.value, c.allow_override, c.override_permissions
            FROM workflow_runtime.constants c
            WHERE c.attribute = :attribute_id AND c.status = 'active'
            LIMIT 1
            """
            
            result = self.db.execute(text(query), {"attribute_id": attribute_id}).fetchone()
            
            if result:
                return result.value
            else:
                self.logger.warning(f"No constant found for attribute {attribute_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting constant value for {attribute_id}: {str(e)}")
            return None
    
    def _find_item_id_by_attribute_name(self, attribute_name: str, lo_id: str) -> Optional[str]:
        """Find the item_id for a given attribute name within the same LO."""
        try:
            query = """
            SELECT lii.item_id 
            FROM workflow_runtime.lo_input_items lii
            JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
            WHERE ea.name = :attribute_name AND lii.lo_id = :lo_id
            LIMIT 1
            """
            
            result = self.db.execute(text(query), {
                "attribute_name": attribute_name,
                "lo_id": lo_id
            }).fetchone()
            
            return result.item_id if result else None
            
        except Exception as e:
            self.logger.error(f"Error finding item_id for attribute {attribute_name}: {str(e)}")
            return None
    
    def _execute_system_function(self, nested_function_id: str) -> Any:
        """Execute a system function and return its result using V2 adapter."""
        try:
            self.logger.info(f"🔍 DEBUG: Executing system function for nested_function_id: {nested_function_id}")
            
            # Get function definition
            query = """
            SELECT nf.function_name, nf.function_type
            FROM workflow_runtime.lo_nested_functions nf
            WHERE nf.nested_function_id = :nested_function_id
            """
            
            result = self.db.execute(text(query), {"nested_function_id": nested_function_id}).fetchone()
            
            if not result:
                self.logger.warning(f"Nested function {nested_function_id} not found")
                return None
            
            function_name = result.function_name
            self.logger.info(f"🔍 DEBUG: Found function: {function_name}")
            
            # Get function parameters from lo_nested_function_input_items
            params_query = """
            SELECT item_id, slot_id, entity_id, attribute_id, value, data_type
            FROM workflow_runtime.lo_nested_function_input_items
            WHERE nested_function_id = :nested_function_id
            ORDER BY item_id
            """
            
            param_results = self.db.execute(text(params_query), {"nested_function_id": nested_function_id}).fetchall()
            
            # Build parameters for V2 execution
            execution_params = {}
            
            for param_row in param_results:
                entity_id = param_row.entity_id
                attribute_id = param_row.attribute_id
                default_value = param_row.value
                slot_id = param_row.slot_id
                
                self.logger.info(f"🔍 DEBUG: Processing parameter {slot_id} (entity: {entity_id}, attr: {attribute_id})")
                
                # Use default value if provided, otherwise build from entity/attribute
                if default_value is not None:
                    param_value = default_value
                    self.logger.info(f"🔍 DEBUG: Using default value: {param_value}")
                else:
                    param_value = None
                
                # Get attribute name for parameter
                if entity_id and attribute_id:
                    attr_name_query = "SELECT name FROM workflow_runtime.entity_attributes WHERE attribute_id = :attribute_id"
                    attr_result = self.db.execute(text(attr_name_query), {"attribute_id": attribute_id}).fetchone()
                    
                    if attr_result:
                        attribute_name = attr_result.name
                        execution_params[attribute_name] = param_value
                        execution_params["entity"] = entity_id
                        execution_params["attribute"] = attribute_id
                        self.logger.info(f"🔍 DEBUG: Added parameter: {attribute_name} = {param_value}")
                    else:
                        self.logger.warning(f"🔍 DEBUG: Could not find attribute name for {attribute_id}")
                elif slot_id:
                    # Fallback to slot_id
                    simple_name = slot_id.split(".")[-1] if "." in slot_id else slot_id
                    execution_params[simple_name] = param_value
                    self.logger.info(f"🔍 DEBUG: Fallback parameter: {simple_name} = {param_value}")
            
            self.logger.info(f"🔍 DEBUG: Final execution parameters: {execution_params}")
            
            # Execute using V2 adapter
            self.logger.info(f"🔧 V2: Executing {function_name} using V2 adapter")
            from app.services.v2.v2_adapter import execute_nested_function_v2
            
            function_result = execute_nested_function_v2(
                function_name=function_name,
                db_session=self.db,
                input_values=execution_params,
                nested_function_id=nested_function_id
            )
            
            self.logger.info(f"✅ V2: Function {function_name} executed: {function_result}")
            return function_result
            
        except Exception as e:
            self.logger.error(f"Error executing system function {nested_function_id}: {str(e)}")
            return None
